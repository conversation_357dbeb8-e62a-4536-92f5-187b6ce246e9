<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Accedi al tuo account Skèmino per giocare online, partecipare a tornei e sfidare giocatori da tutto il mondo.">
    <meta name="theme-color" content="#172a45">
    <meta name="author" content="Skemino Development Team">
    <meta name="robots" content="noindex, nofollow">
    <title>Skèmino - Accedi</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- Carte decorative -->
    <div class="decorative-cards">
        <div class="card-decoration card-1" style="--rotate: -15deg;"></div>
        <div class="card-decoration card-2" style="--rotate: 10deg;"></div>
        <div class="card-decoration card-3" style="--rotate: 20deg;"></div>
        <div class="card-decoration card-4" style="--rotate: -12deg;"></div>
    </div>

    <div class="auth-container">
        <div class="auth-logo">
            <a href="/" title="Torna alla home">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image home-link">
            </a>
        </div>

        <div class="auth-form-container">
            <h2>Accedi a Skèmino</h2>

            <div id="auth-message" class="auth-message"></div>

            <form id="login-form" class="auth-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i> Nome utente
                    </label>
                    <input type="text" id="username" name="username" placeholder="Inserisci il tuo nome utente" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="password" name="password" placeholder="Inserisci la tua password" required autocomplete="current-password">
                        <button type="button" class="toggle-password" aria-label="Mostra/nascondi password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group remember-me">
                    <input type="checkbox" id="remember-me" name="rememberMe">
                    <label for="remember-me">Ricordami su questo dispositivo</label>
                </div>

                <div class="form-actions">
                    <button type="submit" class="primary-btn">
                        <i class="fas fa-sign-in-alt"></i> Accedi al gioco
                    </button>
                </div>

                <div class="social-login-divider">
                    <span>oppure</span>
                </div>

                <div class="social-login-buttons">
                    <button type="button" id="google-login-btn" class="social-login-btn google-btn">
                        <i class="fab fa-google"></i> Accedi con Google
                    </button>
                </div>

                <div class="auth-links">
                    <a href="/register" class="auth-link">Non hai un account? Registrati</a>
                    <a href="/forgot-password" class="auth-link">Password dimenticata?</a>
                    <a href="/activate-account" class="auth-link">Attiva il tuo account</a>
                </div>
            </form>
        </div>

        <div class="auth-footer">
            <a href="/" class="back-to-home">
                <i class="fas fa-home"></i> Torna alla pagina principale
            </a>
        </div>
    </div>

    <script src="auth.js"></script>
    <script src="js/social-auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // ===== CONTROLLO AUTENTICAZIONE =====
            // Se l'utente è già autenticato, reindirizza alla home-logged
            const loggedInUser = localStorage.getItem('loggedInUser');
            const token = localStorage.getItem('token');
            
            console.log('[LOGIN] Controllo autenticazione:', {loggedInUser, token: token ? 'presente' : 'assente'});
            
            if (loggedInUser && token) {
                console.log('[LOGIN] Utente già autenticato, reindirizzo a /home');
                window.location.href = '/home';
                return;
            }
            // ===== FINE CONTROLLO AUTENTICAZIONE =====
            
            // Controlla se c'è un messaggio di attivazione account
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('activated') === 'true') {
                showMessage('Account attivato con successo! Ora puoi accedere.', 'success');
            }

            // Controlla se c'è un parametro returnTo
            const returnTo = urlParams.get('returnTo');
            if (returnTo) {
                // Aggiungi un campo nascosto al form per mantenere il parametro returnTo
                const form = document.getElementById('login-form');
                if (form) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'returnTo';
                    hiddenInput.value = returnTo;
                    form.appendChild(hiddenInput);
                }
            }

            // Inizializza il form di login
            initLoginForm();

            // Animazione carte decorative
            document.querySelectorAll('.card-decoration').forEach(card => {
                const randomDuration = 10 + Math.random() * 10;
                const randomDelay = Math.random() * 5;
                card.style.animationDuration = `${randomDuration}s`;
                card.style.animationDelay = `${randomDelay}s`;
            });
        });
    </script>
</body>
</html>