<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Registrati a Skèmino per giocare online, partecipare a tornei e sfidare giocatori da tutto il mondo.">
    <meta name="theme-color" content="#172a45">
    <meta name="author" content="Skemino Development Team">
    <meta name="robots" content="noindex, nofollow">
    <title>Skèmino - Registrati</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Stili spostati nel file auth.css per una migliore manutenibilità -->
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-logo">
            <a href="/" title="Torna alla home">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image home-link">
            </a>
        </div>

        <div class="auth-form-container">
            <h2>Crea un nuovo account</h2>

            <div id="auth-message" class="auth-message"></div>

            <form id="register-form" class="auth-form">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i> Nome utente
                    </label>
                    <input type="text" id="username" name="username" required>
                    <small class="form-hint">Almeno 5 caratteri, solo lettere e numeri</small>
                </div>

                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i> Email
                    </label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="form-hint">Almeno 8 caratteri (maiuscola, minuscola, numero, carattere speciale)</small>
                </div>

                <div class="form-group">
                    <label for="confirm-password">
                        <i class="fas fa-lock"></i> Conferma Password
                    </label>
                    <div class="password-input-container">
                        <input type="password" id="confirm-password" name="confirmPassword" required>
                        <button type="button" class="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="password-strength-meter">
                    <div class="strength-bar">
                        <div class="strength-indicator" id="strength-indicator"></div>
                    </div>
                    <div class="strength-text" id="strength-text">Forza password: non inserita</div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="primary-btn">
                        <i class="fas fa-user-plus"></i> Registrati
                    </button>
                </div>

                <div class="social-login-divider">
                    <span>oppure</span>
                </div>

                <div class="social-login-buttons">
                    <button type="button" id="google-register-btn" class="social-login-btn google-btn">
                        <i class="fab fa-google"></i> Registrati con Google
                    </button>
                </div>

                <div class="auth-links">
                    <a href="/login" class="auth-link">Hai già un account? Accedi</a>
                </div>
            </form>
        </div>

        <div class="auth-footer">
            <a href="/" class="back-to-home">
                <i class="fas fa-home"></i> Torna alla home
            </a>
        </div>
    </div>

    <script src="auth.js"></script>
    <script src="js/social-auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // ===== CONTROLLO AUTENTICAZIONE =====
            // Se l'utente è già autenticato, reindirizza alla home-logged
            const loggedInUser = localStorage.getItem('loggedInUser');
            const token = localStorage.getItem('token');
            
            console.log('[REGISTER] Controllo autenticazione:', {loggedInUser, token: token ? 'presente' : 'assente'});
            
            if (loggedInUser && token) {
                console.log('[REGISTER] Utente già autenticato, reindirizzo a /home');
                window.location.href = '/home';
                return;
            }
            // ===== FINE CONTROLLO AUTENTICAZIONE =====
            
            // Controlla se c'è un parametro returnTo
            const urlParams = new URLSearchParams(window.location.search);
            const returnTo = urlParams.get('returnTo');
            if (returnTo) {
                // Aggiungi un campo nascosto al form per mantenere il parametro returnTo
                const form = document.getElementById('register-form');
                if (form) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'returnTo';
                    hiddenInput.value = returnTo;
                    form.appendChild(hiddenInput);
                }
            }

            // Inizializza il form di registrazione
            initRegisterForm();
        });
    </script>
</body>
</html>
