 [VISIBILITY] NON riavvio animazione distribuzione carte, mostro stato attuale
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [VISIBILITY DEBUG] currentGameState presente: true
 [VISIBILITY DEBUG] renderGameState definito: true
 [VISIBILITY DEBUG] isMultiplayerGame: true
 [VISIBILITY DEBUG] currentGameState.mode: online
 [VISIBILITY] Partita multiplayer rilevata, skip renderGameState per evitare corruzione stato
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [DRAG START] Inizio trascinamento carta Scissors-Q del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Scissors Card value: Q Type of value: string
 Current player hand: Array(4)
 Full hand data: [{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Scissors-Q","suit":"Scissors","value":"Q"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Rock-8","suit":"Rock","value":"8"}]
 Card data being sent: Object
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d3 Event target: cell-d3
 [DROP SUCCESS] Carta rilasciata su cella: cell-d3 Drop ID: 1751029134989_qvl6ay1n5 Timestamp: 2025-06-27T12:58:54.989Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751029134989_qvl6ay1n5
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] Q di Scissors su d3
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751029134989_qvl6ay1n5
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d3
 [ONLINE GAME] Posizionamento carta: Object in d3
 [DROP] Carta temporanea aggiunta alla cella: d3
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN_VERTEX_CONTROL] Ricevuto evento vertex control dal server: Object
 [PSN_VERTEX_CONTROL] Notifica PSN per controllo vertice: posizione=f1, isWhite=false
 [PSN API] notifyVertexControlChange chiamata per posizione f1, isWhite=false
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f1 e giocatore black. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f1 (nero) per applicazione futura: Object
 [PSN] Usando handSize dal server per black: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751029134989_qvl6ay1n5
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 2, Nero - Scissors Q su d3 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 2
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(3)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 3
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: -1
 [ADVANTAGE] Percentuale finale: 43.3%
 [DEBUG] Vantaggio calcolato: 43.33%
 [HISTORY] Aggiunta mossa #4 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=3, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-Q
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-Q da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-Q rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [VERTEX CHANGE] Vertex vertex-f1 changed controller from null to black
 [VERTEX PSN] Notifica del cambio di controllo del vertice f1 a black (isWhite=false)
 [PSN API] notifyVertexControlChange chiamata per posizione f1, isWhite=false
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f1 e giocatore black. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f1 (nero) per applicazione futura: Object
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1795
 [TIMER DEBUG] - player2TotalTime: 1790
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 3, Bianco - Rock J su b3. Prossimo giocatore: black. StateTurn: 3
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: -1
 [ADVANTAGE] Percentuale finale: 43.3%
 [DEBUG] Vantaggio calcolato: 43.33%
 [HISTORY] Aggiunta mossa #5 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-J
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-J da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Rock-J rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in b3
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b3
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1792
 [TIMER DEBUG] - player2TotalTime: 1790
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [AUDIO] Riproduzione suono loop.mp3 per nuovo loop
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [PSN] Usando handSize dal server per black: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [DRAG START] Inizio trascinamento carta Rock-7 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: 7 Type of value: string
 Current player hand: Array(3)
 Full hand data: [{"id":"Rock-7","suit":"Rock","value":"7"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Rock-8","suit":"Rock","value":"8"}]
 Card data being sent: Object
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-a2 Event target: cell-a2
 [DROP SUCCESS] Carta rilasciata su cella: cell-a2 Drop ID: 1751029153130_2nt9mb6a2 Timestamp: 2025-06-27T12:59:13.130Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751029153130_2nt9mb6a2
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] 7 di Rock su a2
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751029153130_2nt9mb6a2
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: a2
 [ONLINE GAME] Posizionamento carta: Object in a2
 [DROP] Carta temporanea aggiunta alla cella: a2
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per black: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751029153130_2nt9mb6a2
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 3, Nero - Rock 7 su a2 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 3
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: -1
 [ADVANTAGE] Percentuale finale: 43.3%
 [DEBUG] Vantaggio calcolato: 43.33%
 [HISTORY] Aggiunta mossa #6 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=2, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-7
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-7 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Rock-7 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1792
 [TIMER DEBUG] - player2TotalTime: 1776
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: visible  
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 4, Bianco - Scissors 2 su e2. Prossimo giocatore: black. StateTurn: 4
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (1-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #7 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-2
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-2 da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-2 rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in e2
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in e2
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1787
 [TIMER DEBUG] - player2TotalTime: 1776
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN] Usando handSize dal server per black: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [DRAG START] Inizio trascinamento carta Rock-8 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: 8 Type of value: string
 Current player hand: Array(2)
 Full hand data: [{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Rock-8","suit":"Rock","value":"8"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-e3 Event target: cell-e3
 [DROP SUCCESS] Carta rilasciata su cella: cell-e3 Drop ID: 1751029161580_4xxy5v06u Timestamp: 2025-06-27T12:59:21.580Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751029161580_4xxy5v06u
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] 8 di Rock su e3
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751029161580_4xxy5v06u
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: e3
 [ONLINE GAME] Posizionamento carta: Object in e3
 [DROP] Carta temporanea aggiunta alla cella: e3
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per black: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751029161580_4xxy5v06u
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 4, Nero - Rock 8 su e3. Prossimo giocatore: white. StateTurn: 4
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(1)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 1
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (1-1), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: -1
 [ADVANTAGE] Percentuale finale: 43.3%
 [DEBUG] Vantaggio calcolato: 43.33%
 [HISTORY] Aggiunta mossa #8 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: t34tqeKz9cysG-hVAAAU
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player1-hand: previousSize=1, currentSize=1, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=1, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-8
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-8 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Rock-8 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: t34tqeKz9cysG-hVAAAU
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1787
 [TIMER DEBUG] - player2TotalTime: 1773
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
 [ONLINE GAME MAPPING] state.currentPlayerId: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(1)
 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 1
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 96IRYJ
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-1), Carte (0-1), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: -2
 [ADVANTAGE] Percentuale finale: 36.7%
 [DEBUG] Vantaggio calcolato: 36.66%
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [UPDATE UI] Chat visibility aggiornata per partita online
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=1, currentSize=2, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [CARD DRAG] Rendendo trascinabile ULTIMA carta Paper-J in player2-hand
 [CARD DRAG] Listener aggiunti per ULTIMA carta. Draggable: true, Cursor: grab
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1782
 [TIMER DEBUG] - player2TotalTime: 1773
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client t34tqeKz9cysG-hVAAAU: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [DRAG START] Inizio trascinamento carta Paper-J del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Paper Card value: J Type of value: string
 Current player hand: Array(1)
 Full hand data: [{"id":"Paper-J","suit":"Paper","value":"J"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CHAT] Controllo visibilità chat: Object
 [CHAT] Chat abilitata per il multiplayer
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-a1 Event target: cell-a1
 [DROP SUCCESS] Carta rilasciata su cella: cell-a1 Drop ID: 1751029170714_6y5f1w5o1 Timestamp: 2025-06-27T12:59:30.714Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751029170714_6y5f1w5o1
 Dropped Card Data: Object
script.js:10392 [DROP CELL] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
script.js:10397 [DROP ONLINE] È il mio turno? true
script.js:10403 [DROP ONLINE] Il mio colore: black
script.js:10422 [DROP DEBUG] effectiveTurnPlayerId: player2
script.js:10423 [DROP DEBUG] turnPlayer extracted: Object
script.js:10574 (black) tenta di piazzare [DRAG] J di Paper su a1
script.js:10592 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751029170714_6y5f1w5o1
game-mode-manager.js:129 [GAME MODE] Tentativo di piazzare carta: Object in posizione: a1
online-game.js:232 [ONLINE GAME] Posizionamento carta: Object in a1
script.js:10652 [DROP] Carta temporanea aggiunta alla cella: a1
script.js:10658 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10287 [DRAG END] DropEffect: move
psn-unified.js:1237 [PSN] Usando handSize dal server per black: 1 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:1136 Ricevuto evento gameOver con dati rating: Object
script.js:158 [PERSISTENCE] Stato salvato rimosso
script.js:1140 [PERSISTENCE] Stato salvato rimosso - partita terminata
multiplayer.js:948 [VICTORY] Victory screen gestito da victory-fix.js - skip gestione qui
multiplayer.js:968 Game over: Object
script.js:1179 [PSN_VERTEX_CONTROL] Ricevuto evento vertex control dal server: Object
script.js:1187 [PSN_VERTEX_CONTROL] Notifica PSN per controllo vertice: posizione=a1, isWhite=false
psn-unified.js:135 [PSN API] notifyVertexControlChange chiamata per posizione a1, isWhite=false
psn-unified.js:171 [PSN API] Nessuna mossa ESATTA trovata in history per vertice a1 e giocatore black. Metto in pending.
psn-unified.js:185 [PSN API] Memorizzata informazione di controllo vertice a1 (nero) per applicazione futura: Object
script.js:1198 [SOCKET] Ricevuto evento gameState: Object
script.js:1202 [SOCKET] Chiamando handleGameStateEvent
script.js:11183 [ID SYNC] ID corretto confermato: dEN9ZXD1u4bqaGzMAAAW per username: giggio
script.js:11196 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11197 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11198 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11199 [HANDLE GAME STATE] - currentMode: online
script.js:11200 [HANDLE GAME STATE] - window.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
script.js:11258 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11275 [GAME STATE] Stato ricevuto mentre processingDropId = 1751029170714_6y5f1w5o1
script.js:11279 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 5, Nero - Paper J su a1 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 5
psn-unified.js:1841 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:11972 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11973 [TURN PROTECTION] Turno era protetto: dEN9ZXD1u4bqaGzMAAAW
online-game.js:148 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
online-game.js:149 [ONLINE GAME MAPPING] this.myPlayerId: dEN9ZXD1u4bqaGzMAAAW
online-game.js:150 [ONLINE GAME MAPPING] this.opponentId: t34tqeKz9cysG-hVAAAU
online-game.js:151 [ONLINE GAME MAPPING] state.currentPlayerId: dEN9ZXD1u4bqaGzMAAAW
online-game.js:152 [ONLINE GAME MAPPING] localPlayerData: Object
online-game.js:153 [ONLINE GAME MAPPING] opponentPlayerData: Object
script.js:12170 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12173 [GAME STATE] player1 (white): Array(2)
script.js:12173 [GAME STATE] player2 (black): Array(0)
script.js:12393 [PERMANENT NAMES] Nomi permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"giggio","t34tqeKz9cysG-hVAAAU":"bruscolino"}
script.js:12415 [PERMANENT COLORS] Colori permanenti finali: {"dEN9ZXD1u4bqaGzMAAAW":"black","t34tqeKz9cysG-hVAAAU":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 2
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 0
script.js:12487 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12488 [ANIMATION DEBUG] - isStarting: false
script.js:12489 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12490 [ANIMATION DEBUG] - state.gameId: 96IRYJ
script.js:12491 [ANIMATION DEBUG] - state.gameOver: true
script.js:12492 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12493 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12494 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12495 [ANIMATION DEBUG] - state.mode: online
script.js:12496 [ANIMATION DEBUG] - window.animationsCompletedForGame: 96IRYJ
script.js:12497 [ANIMATION DEBUG] - needsAnimations: false
script.js:14434 [UPDATE UI] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
script.js:13630 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"black","vertex-a6":null,"vertex-f6":null}
script.js:13696 [ADVANTAGE] Componenti: Vertici (0-2), Carte (0-1), Adiacenze (0-1)
script.js:13697 [ADVANTAGE] Valore vantaggio calcolato: -4
script.js:13753 [ADVANTAGE] Percentuale finale: 23.3%
script.js:14462 [DEBUG] Vantaggio calcolato: 23.32%
script.js:13810 [ADVANTAGE] Impostato flag di vittoria a true
script.js:15910 [HISTORY] Aggiunta mossa #9 alla cronologia
script.js:15950 Tentativo di forzare rendering rating...
script.js:15977 Ratings dopo init: Object
script.js:15986 Aggiornamento avatar player1: player1
script.js:15992 Aggiornamento avatar player2: player2
script.js:16271 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
script.js:14534 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14537 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14552 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: dEN9ZXD1u4bqaGzMAAAW
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
script.js:14560 [UPDATE UI] Chat visibility aggiornata per partita online
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14677 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:4816 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=2, hasPlayedCard=false
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: bruscolino
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: giggio
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: bruscolino
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: giggio
script.js:14932 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
script.js:14933 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
script.js:5320 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5333 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14935 [DEBUG] updateGameUI AFTER renderBoard
script.js:14936 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
script.js:14937 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
script.js:5577 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
script.js:5583 [VERTEX CHANGE] Vertex vertex-a1 changed controller from null to black
script.js:5609 [VERTEX PSN] Notifica del cambio di controllo del vertice a1 a black (isWhite=false)
psn-unified.js:135 [PSN API] notifyVertexControlChange chiamata per posizione a1, isWhite=false
psn-unified.js:145 [PSN API] Trovata mossa PaperJ@a1 corrispondente esatta in history per vertice a1 (nero).
psn-unified.js:151 [PSN API] ...gainedVertexControl era già true. Nessuna azione.
script.js:5577 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color black
script.js:14957 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14958 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
victory-fix.js:68 [VICTORY-FIX] Gioco terminato, mostrando il riquadro di vittoria con dettagli completi...
script.js:16271 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: dEN9ZXD1u4bqaGzMAAAW
psn-unified.js:1156 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
script.js:5830 [AUDIO] Riproduzione suono loop.mp3 per nuovo loop
multiplayer.js:956 [VICTORY] Victory screen non mostrato, fallback emergency
victory-fix.js:8 Mostrando il riquadro di vittoria...
victory-fix.js:39 Riquadro di vittoria mostrato.
script.js:1136 Ricevuto evento gameOver con dati rating: Object
script.js:158 [PERSISTENCE] Stato salvato rimosso
script.js:1140 [PERSISTENCE] Stato salvato rimosso - partita terminata
multiplayer.js:1091 [NOTIFICATION] Hai vinto! Rating: 1030 → 1042 (+12)
multiplayer.js:948 [VICTORY] Victory screen gestito da victory-fix.js - skip gestione qui
multiplayer.js:968 Game over: Object
multiplayer.js:954 [VICTORY] Victory screen già mostrato da victory-fix.js con dettagli completi
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
script.js:10672 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: Object
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: undefined, gameMode: 'online', gameId: '96IRYJ', isMultiplayerActive: '96IRYJ'}
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
multiplayer.js:2450 [CHAT] Controllo visibilità chat: {currentGameId: null, isGameRunning: undefined, gameMode: 'online', gameId: '96IRYJ', isMultiplayerActive: '96IRYJ'}
multiplayer.js:2461 [CHAT] Chat abilitata per il multiplayer
