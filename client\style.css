/**
 * Skemino Game - Main Stylesheet
 * Version: 1.1.0
 * Author: Skemino Development Team
 * Description: Main stylesheet for the Skemino game interface
 * Created: 2023
 * Last updated: 2024
 *
 * This stylesheet contains all the styling for the Skemino game interface,
 * including the main menu, game board, player areas, and sidebar elements.
 * It is organized by component and includes responsive design for different screen sizes.
 */

/* ===== Responsive CSS Variables ===== */
:root {
    /* ===== Breakpoints ===== */
    /* These are reference values only - use actual media queries for implementation */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 768px;
    --breakpoint-md: 992px;
    --breakpoint-lg: 1200px;
    --breakpoint-xl: 1600px;
    --breakpoint-xxl: 1920px;

    /* ===== Spacing Scales ===== */
    --space-unit: 8px;
    --space-xxs: calc(0.25 * var(--space-unit));  /* 2px */
    --space-xs: calc(0.5 * var(--space-unit));    /* 4px */
    --space-sm: calc(0.75 * var(--space-unit));   /* 6px */
    --space-md: var(--space-unit);                /* 8px */
    --space-lg: calc(1.5 * var(--space-unit));    /* 12px */
    --space-xl: calc(2 * var(--space-unit));      /* 16px */
    --space-xxl: calc(3 * var(--space-unit));     /* 24px */
    --space-xxxl: calc(4 * var(--space-unit));    /* 32px */

    /* ===== Typography ===== */
    --font-size-base: 16px;
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-md: 1rem;         /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-xxl: 1.5rem;      /* 24px */
    --font-size-xxxl: 2rem;       /* 32px */

    /* Line heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-loose: 1.8;

    /* ===== Game Elements ===== */
    /* Card dimensions with fixed 1.46:1 aspect ratio */
    --card-base-width: 90px;
    --card-base-height: calc(var(--card-base-width) * 1.46);
    --card-border-radius: 8px;

    /* Game board grid */
    --cell-base-width: 6vw;
    --cell-base-height: calc(var(--cell-base-width) * 1.46);

    /* ===== Container dimensions ===== */
    --sidebar-width-collapsed: 60px;
    --sidebar-width-expanded: 240px;
    --player-area-width: minmax(250px, 35%);

    /* ===== Z-indexes ===== */
    --z-index-background: -1;
    --z-index-default: 1;
    --z-index-card: 10;
    --z-index-overlay: 50;
    --z-index-tooltip: 100;
    --z-index-modal: 1000;
}

/* ===== RESPONSIVE DESIGN SYSTEM ===== */
/*
 * BREAKPOINT SYSTEM:
 * ------------------
 * xs: 480px and down   (Smartphones portrait)
 * sm: 768px and down   (Smartphones landscape, small tablets)
 * md: 992px and down   (Tablets, small laptops)
 * lg: 1200px and up    (Desktop monitors, laptops)
 * xl: 1600px and up    (Large desktop monitors)
 * xxl: 1920px and up   (Extra large displays, 4K monitors)
 *
 * HOW TO USE THIS SYSTEM:
 * ----------------------
 * 1. Always use the @media queries with the same breakpoints defined above
 * 2. Use CSS variables (defined in :root) to create responsive designs
 * 3. Maintain the card aspect ratio of 1.46:1 across all breakpoints
 * 4. Ensure minimum tap target sizes for mobile devices (44px minimum)
 * 5. For touch devices, provide visible feedback with :active states
 *
 * For example:
 *
 * @media (max-width: 768px) {
 *     :root {
 *         --card-base-width: 70px;
 *     }
 *
 *     .some-element {
 *         padding: var(--space-sm);
 *     }
 * }
 *
 * This breakpoint system ensures consistency across the application.
 */

/* Extra-small devices (phones, 480px and down) */
@media (max-width: 480px) {
    :root {
        --space-unit: 6px;
        --font-size-base: 14px;
        --card-base-width: 60px;
        --cell-base-width: 5vw;
        --sidebar-width-collapsed: 40px;
        /* Define mobile-specific tap target sizes */
        --tap-target-min: 44px; /* Minimum size for touch targets on mobile */
    }

    /* Optimizations for very small screens */
    #game-container {
        grid-template-columns: var(--sidebar-width-collapsed) 1fr;
        grid-template-rows: 58vh 42vh; /* Slightly more space for player areas */
    }

    #game-board {
        transform: scale(0.85); /* Reduce board size further */
    }

    .card-slot, .card {
        border-radius: calc(var(--card-border-radius) * 0.75); /* Smaller radius */
        min-width: var(--tap-target-min); /* Ensure minimum tap target size */
        min-height: calc(var(--tap-target-min) * 1.46); /* Maintain aspect ratio */
    }

    /* Enhanced touch interactions */
    .card:active, .card-slot:active {
        transform: translateY(-2px) scale(1.02) !important; /* Smaller animation for touch */
        transition: transform 0.1s ease-out !important; /* Faster transition for better feedback */
    }

    /* Reduce hover effects to minimize overlap */
    .card:hover, .card-slot:hover {
        /* Hover temporaneamente disabilitato per evitare flash */
    }

    /* Optimize player areas for very small screens */
    #player1-area, #player2-area {
        padding: var(--space-xxs);
    }

    /* Enhance buttons for touch */
    button {
        min-height: var(--tap-target-min) !important; /* Ensure buttons are easy to tap */
        padding: var(--space-sm) var(--space-md) !important;
        touch-action: manipulation !important; /* Better touch handling */
    }

    /* Improved touch feedback */
    button:active {
        transform: translateY(1px) !important;
        transition: transform 0.1s ease-out !important;
    }

    /* Increase spacing between interactive elements */
    .menu-buttons, nav ul {
        gap: var(--space-md) !important;
    }
}

/* Small devices (tablets, 768px and down) */
@media (max-width: 768px) {
    :root {
        --space-unit: 7px;
        --font-size-base: 15px;
        --card-base-width: 70px;
        --cell-base-width: 5.5vw;
        --sidebar-width-collapsed: 50px;
        --tap-target-min: 40px; /* Slightly smaller than phones but still touch-friendly */
    }

    /* Touch optimizations for tablets */
    button,
    .card,
    .card-slot,
    .sidebar li,
    .menu-icon {
        cursor: pointer;
        touch-action: manipulation; /* Better touch handling */
    }

    /* Active states for touch feedback on important interactions */
    .card:active,
    .card-slot:active,
    button:active,
    .sidebar li:active {
        transition: all 0.15s ease-out !important;
    }
}

/* Medium devices (tablets, 992px and down) */
@media (max-width: 992px) {
    :root {
        --card-base-width: 80px;
        --cell-base-height: 5.8vw;
    }
}

/* Large devices (desktops, 1200px and up) */
@media (min-width: 1200px) {
    :root {
        --space-unit: 9px;
        --font-size-base: 17px;
    }
}

/* Extra-large devices (large desktops, 1600px and up) */
@media (min-width: 1600px) {
    :root {
        --space-unit: 10px;
        --font-size-base: 18px;
        --card-base-width: 100px;
        --cell-base-width: 6.5vw;
    }
}

/* Extra-extra-large devices (wide screens, 1920px and up) */
@media (min-width: 1920px) {
    :root {
        --space-unit: 11px;
        --font-size-base: 19px;
        --card-base-width: 110px;
        --cell-base-width: 7vw;
    }
}

html, body {
    min-height: 100vh; /* Changed from height: 100% */
    margin: 0;
    padding: 0;
    overflow-y: auto; /* Changed from overflow: hidden */
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, #0a1218, #162736);
    color: #ffffff;
    background-image:
        linear-gradient(135deg, #0a1218, #162736),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.03)' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* ===== Touch Optimization ===== */
html {
    /* Touch behavior controls */
    touch-action: manipulation !important; /* Better for mobile touch interactions than 'none' */
    -ms-touch-action: manipulation !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    scroll-behavior: smooth; /* Aggiunge scorrimento fluido per la pagina */
    user-select: none !important;
    -ms-content-zooming: none !important;
    -moz-user-select: none !important;
    overscroll-behavior: none !important;

    /* Touch delay improvement for iOS devices */
    -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
}

body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;

    /* Responsive typography baseline */
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#main-menu, #player-names-screen {
    text-align: center;
    background: linear-gradient(135deg, #ffffff 0%, #f0f2f5 100%);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3), 0 0 0 2px rgba(255,255,255,0.2);
    margin-bottom: 30px;
    max-width: 550px;
    width: 90%;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(0,0,0,0.1);
}

#main-menu:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.25);
}

.menu-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 25px;
}

.menu-icon {
    font-size: 3em;
    color: #4CAF50;
    animation: pulse 2.5s infinite;
    text-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.15); opacity: 0.9; }
    100% { transform: scale(1); opacity: 1; }
}

#main-menu h1 {
    margin: 0;
    color: #2c3e50;
    font-size: var(--font-size-xxxl); /* Use responsive variable */
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    letter-spacing: 0.05em;
}

.menu-subtitle {
    color: #7f8c8d;
    font-size: var(--font-size-lg); /* Use responsive variable */
    margin-bottom: var(--space-xxl); /* Use spacing variable */
    font-style: italic;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

#main-menu button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    border: none;
    border-radius: var(--card-border-radius);
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 6px 12px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.15);
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

#main-menu button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: 0.5s;
}

#main-menu button:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.35), 0 0 0 2px rgba(255,255,255,0.2);
    background: linear-gradient(135deg, #27ae60 0%, #219653 100%);
}

#main-menu button:hover:before {
    left: 100%;
}

#main-menu button:active {
    transform: translateY(0);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#main-menu button i {
    font-size: 1.3em;
}

#main-menu #menu-message {
    margin-top: 20px;
    font-style: italic;
    color: #7f8c8d;
    font-size: 0.9em;
}

#game-container {
    display: grid;
    grid-template-areas:
        "sidebar players board";
    grid-template-columns: var(--sidebar-width-collapsed) minmax(min(520px, 35%), 35%) minmax(800px, 1fr); /* Board-area più largo e stabile */
    grid-template-rows: 100%;
    gap: var(--space-sm); /* Usa variabili CSS per spacing consistente */
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    max-height: 100vh;
    background-color: rgba(20, 30, 45, 0.95);
    padding: 0;
    margin: 0;
    border-radius: 0;
    box-shadow: 0 0 40px rgba(0,0,0,0.7), 0 0 0 3px rgba(255,255,255,0.1), inset 0 0 100px rgba(0,0,0,0.2);
    border: none;
    box-sizing: border-box;
    overflow: hidden; /* Impedisce qualsiasi scorrimento */
    backdrop-filter: blur(10px);
    position: relative;
}

#game-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(to bottom, rgba(255,255,255,0.05), transparent);
    pointer-events: none;
    z-index: 1;
}

/* Vertici del tabellone */
.vertex-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: auto; /* Modificato per permettere interazioni */
    z-index: 50; /* Aumentato per essere sicuri che sia sopra altri elementi */
}

.vertex {
    display: block !important; /* Modifica da 'none' a 'block' per renderli sempre visibili */
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    background-color: rgba(240, 240, 240, 0.7) !important;
    border: 4px solid #111 !important;
    position: absolute !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    box-shadow: 0 0 20px rgba(0,0,0,0.6), 0 0 30px rgba(255,255,255,0.5) !important;
    animation: vertexPulse 2s infinite alternate !important;
    z-index: 100 !important; /* Aumentato ulteriormente per essere sicuri che sia sopra altri elementi */
    pointer-events: auto !important; /* Assicura che il vertice sia cliccabile */
}

/* Posizionamento dei vertici rispetto alle celle */
#game-board {
    position: relative !important;
}

/* Posizionamento specifico per ogni vertice */
#vertex-a1 {
    position: absolute !important;
    top: 0 !important; /* Angolo in alto a sinistra */
    left: 0 !important;
    transform: translate(0, 0) !important;
}

#vertex-f1 {
    position: absolute !important;
    top: 0 !important; /* Angolo in alto a destra */
    right: 0 !important;
    left: auto !important;
    transform: translate(0, 0) !important;
}

#vertex-a6 {
    position: absolute !important;
    bottom: 0 !important; /* Angolo in basso a sinistra */
    left: 0 !important;
    top: auto !important;
    transform: translate(0, 0) !important;
}

#vertex-f6 {
    position: absolute !important;
    bottom: 0 !important; /* Angolo in basso a destra */
    right: 0 !important;
    top: auto !important;
    left: auto !important;
    transform: translate(0, 0) !important;
}

.vertex::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    z-index: 101 !important;
}

/* Notazione per i vertici */
.vertex::before {
    content: attr(id) !important;
    content: attr(id) !important;
    position: absolute !important;
    bottom: -5px !important;
    right: -5px !important;
    font-size: 12px !important;
    line-height: 1 !important;
    color: rgba(220, 235, 255, 0.95) !important;
    font-weight: bold !important;
    pointer-events: none !important;
    z-index: 102 !important; /* Superiore al token (101) */
    background-color: rgba(20, 40, 80, 0.85) !important;
    padding: 3px 6px !important;
    border-radius: 6px !important;
    opacity: 0.9 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25), 0 0 10px rgba(30, 120, 255, 0.3) !important;
    text-shadow: 0 0 5px rgba(30, 120, 255, 0.6) !important;
    border: 1px solid rgba(120, 170, 255, 0.4) !important;
    backdrop-filter: blur(2px) !important;
    transform: translate(0, 0) !important;
}

/* Modifica il contenuto per mostrare solo la notazione (a1, f1, a6, f6) */
#vertex-a1::before { content: 'a1' !important; }
#vertex-f1::before { content: 'f1' !important; }
#vertex-a6::before { content: 'a6' !important; }
#vertex-f6::before { content: 'f6' !important; }

/* Stili per i token dei vertici */
.vertex-token {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    z-index: 101; /* Sopra il vertice ma sotto la notazione */
    transition: all 0.3s ease;
    pointer-events: none; /* Non interferisce con i click sul vertice */
}

.vertex-token.white {
    background-color: #ffffff;
    border: 2px solid #000;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.9);
    animation: tokenPulseWhite 1.5s infinite alternate;
}

.vertex-token.black {
    background-color: #000000;
    border: 2px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.9);
    animation: tokenPulseBlack 1.5s infinite alternate;
}

/* FIX: Migliora la visibilità del vertice controllato dal giocatore bianco */
.vertex[data-controller="white"] {
    background-color: rgba(255, 255, 255, 0.95) !important; /* Più opaco e importante */
    border-color: #000 !important;
    border-width: 4px !important;
    box-shadow: 0 0 20px rgba(255,255,255,0.9), 0 0 40px rgba(255,255,255,0.5) !important; /* Doppio alone */
    animation: capturedVertexWhite 2s infinite alternate;
    transform: scale(1.1) !important;
}

.vertex[data-controller="white"]::after {
    background-color: #ffffff;
    border: 2px solid #000;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.9);
    opacity: 1;
    animation: tokenPulseWhite 1.5s infinite alternate;
}

/* Assicura che la notazione rimanga visibile anche quando il vertice è controllato */
.vertex[data-controller="white"]::before {
    z-index: 102 !important; /* Superiore al token */
    opacity: 1 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), 0 0 15px rgba(255, 255, 255, 0.5) !important;
    background-color: rgba(20, 40, 80, 0.9) !important;
}

/* FIX: Migliora la visibilità del vertice controllato dal giocatore nero */
.vertex[data-controller="black"] {
    background-color: rgba(0, 0, 0, 0.95) !important; /* Più opaco e importante */
    border-color: #fff !important;
    border-width: 4px !important;
    box-shadow: 0 0 20px rgba(0,0,0,0.9), 0 0 40px rgba(0,0,0,0.5) !important; /* Doppio alone */
    animation: capturedVertexBlack 2s infinite alternate;
    transform: scale(1.1) !important;
}

.vertex[data-controller="black"]::after {
    background-color: #000000;
    border: 2px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.9);
    opacity: 1;
    animation: tokenPulseBlack 1.5s infinite alternate;
}

/* Assicura che la notazione rimanga visibile anche quando il vertice è controllato */
.vertex[data-controller="black"]::before {
    z-index: 102 !important; /* Superiore al token */
    opacity: 1 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 0, 0, 0.5) !important;
    background-color: rgba(20, 40, 80, 0.9) !important;
}

/* FIX: CSS specifico per lo stato 'none' - aspetto neutrale metà bianco e metà nero */
.vertex[data-controller="none"] {
    background: linear-gradient(to right, #ffffff 50%, #000000 50%) !important; /* Metà bianco, metà nero */
    border: 4px solid #555 !important; /* Bordo grigio per i neutrali */
    box-shadow: 0 0 15px rgba(150,150,150,0.7) !important; /* Box-shadow neutrale */
    animation: vertexPulseNeutral 2s infinite alternate !important; /* Animazione specifica per neutrali */
}

/* BUGFIX MASSIMA PRIORITÀ: Forza l'aspetto neutrale anche con altre classi */
.vertex[data-controller="none"],
.vertex[data-controller="none"].playable-vertex,
.vertex[data-controller="none"].neutral-vertex {
    background: linear-gradient(to right, #ffffff 50%, #000000 50%) !important;
    background-color: transparent !important; /* Previene override del background-color */
}

.vertex[data-controller="none"]::after {
    opacity: 0; /* Nessun token visibile */
}

/* Assicura che la notazione rimanga visibile anche quando il vertice è neutro */
.vertex[data-controller="none"]::before {
    z-index: 102 !important; /* Superiore al token */
    opacity: 1 !important;
    color: rgba(220, 235, 255, 0.95) !important; /* Stesso colore di base */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3), 0 0 15px rgba(255, 255, 255, 0.5) !important;
    background-color: rgba(20, 40, 80, 0.9) !important;
}

@keyframes vertexPulse {
    0% { transform: scale(1); box-shadow: 0 0 15px rgba(0,0,0,0.4), 0 0 20px rgba(255,255,255,0.2); }
    100% { transform: scale(1.15); box-shadow: 0 0 25px rgba(0,0,0,0.6), 0 0 35px rgba(255,255,255,0.4); }
}

@keyframes vertexPulseNeutral {
    0% { transform: scale(1); box-shadow: 0 0 15px rgba(150,150,150,0.5); }
    50% { transform: scale(1.1); box-shadow: 0 0 25px rgba(150,150,150,0.8); }
    100% { transform: scale(1.05); box-shadow: 0 0 20px rgba(150,150,150,0.6); }
}

@keyframes capturedVertexWhite {
    0% { transform: scale(1); box-shadow: 0 0 10px rgba(255,255,255,0.5), 0 0 20px rgba(255,255,255,0.3); }
    100% { transform: scale(1.15); box-shadow: 0 0 25px rgba(255,255,255,0.8), 0 0 50px rgba(255,255,255,0.5); }
}

@keyframes capturedVertexBlack {
    0% { transform: scale(1); box-shadow: 0 0 10px rgba(0,0,0,0.5), 0 0 20px rgba(0,0,0,0.3); }
    100% { transform: scale(1.15); box-shadow: 0 0 25px rgba(0,0,0,0.8), 0 0 50px rgba(0,0,0,0.5); }
}

/* Animazioni per il cambio di controllo dei vertici */
@keyframes vertexChangeWhite {
    0% { transform: scale(1); box-shadow: 0 0 0 rgba(255, 255, 255, 0); }
    25% { transform: scale(1.5); box-shadow: 0 0 30px rgba(255, 255, 255, 0.8); }
    50% { transform: scale(1.2); box-shadow: 0 0 20px rgba(255, 255, 255, 0.6); }
    75% { transform: scale(1.3); box-shadow: 0 0 25px rgba(255, 255, 255, 0.7); }
    100% { transform: scale(1); box-shadow: 0 0 0 rgba(255, 255, 255, 0); }
}

@keyframes vertexChangeBlack {
    0% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
    25% { transform: scale(1.5); box-shadow: 0 0 30px rgba(0, 0, 0, 0.8); }
    50% { transform: scale(1.2); box-shadow: 0 0 20px rgba(0, 0, 0, 0.6); }
    75% { transform: scale(1.3); box-shadow: 0 0 25px rgba(0, 0, 0, 0.7); }
    100% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
}

.vertex-change-white {
    animation: vertexChangeWhite 1.5s ease-in-out;
    z-index: 100;
}

.vertex-change-black {
    animation: vertexChangeBlack 1.5s ease-in-out;
    z-index: 100;
}

@keyframes tokenPulseWhite {
    0% { transform: translate(-50%, -50%) scale(0.8); box-shadow: 0 0 5px rgba(255,255,255,0.5); }
    100% { transform: translate(-50%, -50%) scale(1.2); box-shadow: 0 0 15px rgba(255,255,255,0.9); }
}

@keyframes tokenPulseBlack {
    0% { transform: translate(-50%, -50%) scale(0.8); box-shadow: 0 0 5px rgba(0,0,0,0.5); }
    100% { transform: translate(-50%, -50%) scale(1.2); box-shadow: 0 0 15px rgba(0,0,0,0.9); }
}

/* Animazione tokenPulseNeutral rimossa - non più utilizzata */

/* Menu laterale compresso per l'interfaccia di gioco */
.game-sidebar {
    grid-area: sidebar;
    width: 60px; /* Menu molto compresso */
    height: 100vh;
    background-color: rgba(10, 20, 35, 0.95);
    color: #e0f0ff;
    display: flex;
    flex-direction: column;
    padding: 0;
    padding-bottom: 15px;
    box-shadow: 5px 0 25px rgba(0,0,0,0.5), 0 0 50px rgba(0, 30, 60, 0.5);
    z-index: 10;
    border-right: 1px solid rgba(100, 150, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: visible; /* Modificato per permettere ai tooltip di essere visibili */
    transition: width 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 0;
    border-radius: 0;
    align-items: center; /* Centramento orizzontale */
    box-sizing: border-box; /* Assicura che padding e bordi siano inclusi nelle dimensioni */
}

.game-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.2;
    pointer-events: none;
    z-index: -1;
}

.game-sidebar .logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0 15px 0;
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
    position: relative;
}

.game-sidebar .logo .logo-image {
    width: 40px;
    height: auto;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    margin-left: auto;
    margin-right: auto;
    display: block;
    position: relative;
    left: 0; /* Resetta qualsiasi offset */
    cursor: pointer;
}

/* Rimosso l'espansione al passaggio del mouse */

.game-sidebar nav {
    flex: 1;
    margin-top: 20px;
}

.game-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.game-sidebar li {
    padding: 14px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 5px 0;
    border-left: 0px solid rgba(50, 220, 100, 0);
    color: #c0d0ff;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.game-sidebar li i {
    font-size: 20px;
    width: 60px;
    text-align: center;
    color: rgba(50, 220, 100, 0.8);
    text-shadow: 0 0 10px rgba(50, 220, 100, 0.5);
    transition: color 0.3s ease, text-shadow 0.3s ease;
    /* Manteniamo la posizione fissa */
    display: inline-block;
    position: relative;
}

.game-sidebar li span {
    display: none; /* Nascosto nel menu compresso */
}

/* Stile per i tooltip/etichette al passaggio del mouse sulle icone */
.game-sidebar li {
    position: relative;
}

/* Tooltip semplificato */
.game-sidebar li {
    position: relative;
}

.game-sidebar li:hover .menu-tooltip {
    display: none !important; /* Non mostrare mai i tooltip */
}

.game-sidebar li .menu-tooltip {
    display: none !important; /* Nascondi completamente i tooltip */
    position: absolute;
    left: 65px; /* Larghezza fissa della sidebar + spazio per la freccia */
    top: 50%;
    transform: translateY(-50%);
    background: rgba(30, 60, 120, 0.95);
    color: #e0f0ff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(50, 220, 100, 0.2);
    border: 1px solid rgba(50, 220, 100, 0.4);
    z-index: 1000;
    pointer-events: none;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
    animation: fadeIn 0.2s ease-out;
    letter-spacing: 0.5px;
}

/* Freccia del tooltip */
.game-sidebar li .menu-tooltip:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent rgba(30, 60, 120, 0.95) transparent transparent;
    z-index: 1001;
    pointer-events: none;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-50%) translateX(-10px); }
    to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

/* Rimosso lo stile degli elementi li al passaggio del mouse sulla sidebar */

/* Effetto di sfondo al passaggio del mouse */
.game-sidebar li {
    overflow: visible !important; /* Assicura che il tooltip sia visibile */
}

.game-sidebar li::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.2), transparent);
    transition: width 0.3s ease;
    z-index: -1;
}

.game-sidebar li:hover {
    background-color: rgba(30, 60, 120, 0.3);
    color: #e0f0ff;
    /* Assicuriamoci che non ci siano trasformazioni che spostano l'elemento */
    transform: none;
}

/* Rimosso lo stile degli elementi li:hover al passaggio del mouse sulla sidebar */

.game-sidebar li:hover::after {
    width: 100%;
}

.game-sidebar li.active {
    background-color: rgba(30, 60, 120, 0.5);
    color: #e0f0ff;
}

/* Rimosso lo stile degli elementi li.active al passaggio del mouse sulla sidebar */

.game-sidebar li:hover i {
    /* Rimuoviamo transform e animation per evitare conflitti */
    color: rgba(50, 220, 100, 1);
    text-shadow: 0 0 15px rgba(50, 220, 100, 0.8);
    /* Utilizziamo una nuova animazione che non modifica la posizione */
    animation: pulseColor 1s infinite alternate;
}

@keyframes pulseColor {
    0% {
        color: rgba(50, 220, 100, 0.8);
        text-shadow: 0 0 10px rgba(50, 220, 100, 0.5);
    }
    100% {
        color: rgba(50, 220, 100, 1);
        text-shadow: 0 0 20px rgba(50, 220, 100, 0.9);
    }
}

.game-sidebar .sidebar-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
    margin-top: auto;
    position: relative;
    width: 100%;
    border-top: 1px solid rgba(100, 150, 255, 0.2);
}

.game-sidebar .sidebar-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(100, 150, 255, 0.4), transparent);
    box-shadow: 0 0 10px rgba(100, 150, 255, 0.3);
}

/* Stile per l'etichetta utente nel menu laterale di gioco */
#game-user-profile {
    padding: 14px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 5px 0;
    border-left: 0px solid rgba(50, 220, 100, 0);
    color: #c0d0ff;
    position: relative;
    overflow: visible !important;
    white-space: nowrap;
}

#game-user-profile::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.2), transparent);
    transition: width 0.3s ease;
    z-index: -1;
}

#game-user-profile:hover {
    background-color: rgba(30, 60, 120, 0.3);
    color: #e0f0ff;
    transform: none;
}

#game-user-profile:hover::after {
    width: 100%;
}

#game-user-profile i {
    font-size: 20px;
    width: 60px;
    text-align: center;
    color: rgba(50, 220, 100, 0.8);
    text-shadow: 0 0 10px rgba(50, 220, 100, 0.5);
    transition: color 0.3s ease, text-shadow 0.3s ease;
    display: inline-block;
    position: relative;
}

#game-user-profile:hover i {
    color: rgba(50, 220, 100, 1);
    text-shadow: 0 0 15px rgba(50, 220, 100, 0.8);
    animation: pulseColor 1s infinite alternate;
}

#game-user-profile span {
    display: none; /* Nascosto nel menu compresso */
}

/* Stile per il tooltip dell'utente quando il menu è compresso */
#game-user-profile .menu-tooltip {
    display: none;
    position: absolute;
    left: 65px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(30, 60, 120, 0.95);
    color: #e0f0ff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9em;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(50, 220, 100, 0.2);
    border: 1px solid rgba(50, 220, 100, 0.4);
    z-index: 1000;
    pointer-events: none;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
    animation: fadeIn 0.2s ease-out;
    letter-spacing: 0.5px;
}

/* Freccia del tooltip */
#game-user-profile .menu-tooltip:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent rgba(30, 60, 120, 0.95) transparent transparent;
    z-index: 1001;
    pointer-events: none;
}

#game-user-profile:hover .menu-tooltip {
    display: block;
}

/* Nuova area per i giocatori */
#players-column {
    grid-area: players;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: var(--space-xl); /* Spacing tra i player areas */
    height: 100%;
    padding: var(--space-xs) var(--space-xs) var(--space-lg) var(--space-xs); /* Padding responsivo */
    box-sizing: border-box;
}

#player1-area, #player2-area {
    text-align: center;
    padding: var(--space-xs); /* Usa variabili CSS per spacing consistente */
    background-color: rgba(15, 25, 40, 0.8);
    border-radius: var(--card-border-radius);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1; /* Distribuzione flessibile dello spazio */
    min-height: 0; /* Permette di rimpicciolirsi sotto flex: 1 */
    box-shadow: 0 5px 15px rgba(0,0,0,0.4), inset 0 1px 1px rgba(255,255,255,0.1), 0 0 20px rgba(0, 100, 255, 0.1);
    transition: none;
    border: 2px solid rgba(30, 60, 100, 0.5);
    backdrop-filter: blur(5px);
    position: relative;
    box-sizing: border-box; /* Assicura che padding sia incluso nella larghezza */
    margin: 0; /* Reset eventuali margin inconsistenti */
}

/* Player areas flex container adjustments */
#player1-area {
    margin-bottom: var(--space-sm); /* Spazio tra i due pannelli giocatore */
}

#player2-area {
    margin-top: var(--space-sm); /* Spazio tra i due pannelli giocatore */
}

#player1-area::before, #player2-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
    pointer-events: none;
    z-index: 0;
}

/* Header del giocatore con timer */
.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xs);
    border-bottom: 1px solid rgba(100, 150, 255, 0.4);
    padding: 0 var(--space-xs) var(--space-sm) var(--space-xs);
    position: relative;
    z-index: var(--z-index-default);
    flex-wrap: wrap;
}

#player1-area h2, #player2-area h2 {
    margin: 0;
    font-size: var(--font-size-lg); /* Uso variabile CSS */
    color: #e0e0ff;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.7);
    letter-spacing: 0.05em;
    position: relative;
    z-index: var(--z-index-default);
}

.player-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(100, 150, 255, 0.8), transparent);
}

/* Timer del giocatore */
.player-timer {
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0.5;
    transition: opacity 0.3s ease;
    padding: 5px 8px;
    background: rgba(20, 40, 80, 0.4);
    border-radius: 8px;
    border: 1px solid rgba(100, 150, 255, 0.3);
}

.player-timer.active {
    opacity: 1;
    box-shadow: 0 0 15px rgba(100, 150, 255, 0.4);
}

/* Timer totale del giocatore */
.total-timer {
    display: flex;
    align-items: center;
    margin-left: -15px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.total-timer i {
    color: #f8cd38;
    margin-right: 8px;
    font-size: 0.9em;
}

.total-timer-count {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #fff;
    font-size: 0.9em;
}

/* Stile del contatore */
.timer-count {
    font-size: 1.3em;
    font-weight: bold;
    color: #e0f0ff;
    min-width: 30px;
    text-align: center;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.7);
}

/* Timer con poco tempo rimasto */
.timer-count.warning {
    color: #ffcc00;
    text-shadow: 0 0 10px rgba(255, 204, 0, 0.7);
    animation: pulseWarning 1s infinite alternate;
    font-size: 1.4em;
}

/* Timer quasi scaduto */
.timer-count.danger {
    color: #ff3b30;
    text-shadow: 0 0 10px rgba(255, 59, 48, 0.7), 0 0 20px rgba(255, 59, 48, 0.5);
    animation: pulseDanger 0.5s infinite alternate;
    font-size: 1.5em;
}

/* Effetti sul contenitore del timer quando è in stato di avviso o pericolo */
.player-timer.active .timer-count.warning ~ .hourglass {
    filter: drop-shadow(0 0 8px rgba(255, 204, 0, 0.7));
}

.player-timer.active .timer-count.danger ~ .hourglass {
    filter: drop-shadow(0 0 12px rgba(255, 59, 48, 0.7));
    transform: scale(1.15);
}

@keyframes pulseWarning {
    from { opacity: 0.8; transform: scale(1); }
    to { opacity: 1; transform: scale(1.1); }
}

@keyframes pulseDanger {
    from { opacity: 0.8; transform: scale(1); }
    to { opacity: 1; transform: scale(1.2); }
}

/* Clessidra */
.hourglass {
    position: relative;
    width: 22px;
    height: 34px;
    transform-style: preserve-3d;
    perspective: 600px;
    margin: 2px;
}

.hourglass-top, .hourglass-bottom {
    position: absolute;
    width: 100%;
    height: 50%;
    background: rgba(100, 150, 255, 0.8);
    box-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.hourglass-top {
    top: 0;
    clip-path: polygon(0 0, 100% 0, 80% 100%, 20% 100%);
    transform-origin: bottom center;
    animation: emptyTop 1800s linear forwards paused;
}

.hourglass-bottom {
    bottom: 0;
    clip-path: polygon(20% 0, 80% 0, 100% 100%, 0 100%);
    transform-origin: top center;
    animation: fillBottom 1800s linear forwards paused;
}

.player-timer.active .hourglass {
    transform: scale(1.1);
    filter: drop-shadow(0 0 5px rgba(100, 150, 255, 0.7));
}

.player-timer.active .hourglass-top {
    animation-play-state: running;
}

.player-timer.active .hourglass-bottom {
    animation-play-state: running;
}

@keyframes emptyTop {
    0% { transform: rotateX(180deg); background: rgba(255, 59, 48, 0.8); }
    20% { background: rgba(255, 59, 48, 0.8); }
    40% { background: rgba(255, 204, 0, 0.8); }
    60% { background: rgba(100, 150, 255, 0.8); }
    100% { transform: rotateX(0); background: rgba(100, 150, 255, 0.8); }
}

@keyframes fillBottom {
    0% { transform: rotateX(180deg); background: rgba(180, 40, 30, 0.3); }
    20% { background: rgba(180, 40, 30, 0.3); }
    40% { background: rgba(180, 140, 0, 0.3); }
    60% { background: rgba(30, 60, 120, 0.3); }
    100% { transform: rotateX(0); background: rgba(30, 60, 120, 0.3); }
}

/* Elementi info rimossi per guadagnare spazio */

#board-area {
    grid-area: board;
    display: grid;
    grid-template-columns: 30px minmax(0, 1fr) min(25%, 300px); /* minmax(0,1fr) gestisce gli overflow */
    grid-template-areas:
        "advantage game-board board-sidebar";
    gap: var(--space-lg); /* Usa variabili CSS per spacing consistente */
    width: 100%;
    height: 100%; /* Usa tutto lo spazio disponibile */
    position: relative;
    overflow: hidden; /* Impedisce qualsiasi scorrimento */
    align-items: stretch;
    background: linear-gradient(135deg, #1a2a3a, #0d1520);
    border-radius: var(--card-border-radius); /* Usa la variabile CSS */
    padding: var(--space-lg);
    margin: 0; /* Reset margin */
    box-shadow: inset 0 0 40px rgba(0,0,0,0.5), 0 5px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.1);
    border: 2px solid rgba(30, 60, 100, 0.5);
    box-sizing: border-box;

    /* Grid responsivo che si adatta alle dimensioni dello schermo */
    display: grid;
    align-items: stretch; /* Estende le celle verticalmente */
    justify-content: stretch; /* Estende le celle orizzontalmente */
}

#board-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 50% 50%, rgba(30, 60, 120, 0.1) 0%, transparent 70%),
        url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M50 50c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10s-10-4.477-10-10 4.477-10 10-10zm10 8c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8zm40 40c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    opacity: 0.5;
    border-radius: 8px; /* Aggiunto bordo arrotondato */
}

#game-message-container {
    text-align: center;
    width: 90%;
    padding: 10px 15px;
    background: linear-gradient(to bottom, rgba(20, 40, 80, 0.8), rgba(10, 20, 40, 0.8));
    border-radius: 10px;
    box-sizing: border-box;
    box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.25), inset 0 0 20px rgba(0, 30, 60, 0.5);
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(50, 100, 200, 0.4);
    transform: translateZ(10px);
    backdrop-filter: blur(5px);
    position: relative;
    z-index: 5;
    min-width: 0;
    overflow: hidden;
    margin: 0 auto 15px auto;
    max-width: 100%;
}

#game-message-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22.24H0v-1.41zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.48H0V3.07zm20.76 35.52l2.83-2.83 1.41 1.41L22.17 40h-1.41v-1.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83h-1.41v-1.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83h-1.41V3.07zm-12.3 35.52l2.83-2.83 1.41 1.41L9.88 40H8.47v-1.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83H8.47v-1.41zm0-17.76l2.83-2.83 1.41 1.41-2.83 2.83H8.47V3.07zm38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83V22.24h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83V4.48h-1.41zM20.83 40l-2.83-2.83 1.41-1.41 2.83 2.83V40h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83v1.41h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83v1.41h-1.41zm17.76 35.52l-2.83-2.83 1.41-1.41 2.83 2.83V40h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83v1.41h-1.41zm0-17.76l-2.83-2.83 1.41-1.41 2.83 2.83v1.41h-1.41z'/%3E%3C/g%3E%3C/svg%3E");
    border-radius: 10px;
    pointer-events: none;
    z-index: -1;
}



#game-dice-result {
    font-weight: bold;
    padding: 12px 20px;
    border-radius: 10px;
    min-width: 0;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.4), 0 0 20px rgba(0, 100, 255, 0.2), inset 0 0 10px rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(100, 150, 255, 0.3);
    font-size: 1.05em;
    background: rgba(20, 40, 80, 0.6);
    color: #e0f0ff;
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.8);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    overflow: hidden;
    /* Modifiche per gestire meglio il testo lungo */
    white-space: normal;
    word-wrap: break-word;
    min-height: 60px; /* Altezza minima per ospitare più righe di testo */
    display: flex;
    align-items: center;
    justify-content: center;
}

#current-player:hover, #score:hover, #game-dice-result:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.5), 0 0 30px rgba(0, 100, 255, 0.3);
}

#current-player {
    background: linear-gradient(135deg, rgba(20, 80, 180, 0.7), rgba(10, 40, 100, 0.7));
}

#score {
    background: linear-gradient(135deg, rgba(100, 40, 180, 0.7), rgba(60, 20, 120, 0.7));
}

#game-dice-result {
    background: linear-gradient(135deg, rgba(180, 60, 20, 0.7), rgba(120, 30, 10, 0.7));
}

#game-message {
    min-height: 1.5em;
    color: #ff9090;
    font-weight: bold;
    animation: fadeInOut 2s ease-in-out;
    font-size: 21px; /* Modificato da 1em */
    text-shadow: 0 0 10px rgba(255, 50, 50, 0.6);
    background: linear-gradient(135deg, rgba(80, 20, 20, 0.6), rgba(40, 10, 10, 0.6));
    border-radius: 10px;
    padding: 12px 15px;
    margin: 0;
    border: 1px solid rgba(255, 100, 100, 0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3), 0 0 20px rgba(255, 50, 50, 0.2);
    backdrop-filter: blur(5px);
    width: 100%;
    box-sizing: border-box;
    text-align: center;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    15% { opacity: 1; transform: translateY(0); }
    85% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0.8; transform: translateY(0); }
}

#game-info button {
    margin: 15px auto;
    padding: 16px 30px;
    font-size: 1.1em;
    cursor: pointer;
    background: linear-gradient(135deg, rgba(20, 120, 220, 0.8), rgba(10, 60, 150, 0.8));
    color: #e0f0ff;
    border: none;
    border-radius: 25px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 6px 20px rgba(0,0,0,0.4), 0 0 40px rgba(0, 100, 255, 0.3), inset 0 0 15px rgba(255, 255, 255, 0.1);
    max-width: 100%;
    min-width: 0;
    width: 100%;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 100, 255, 0.6);
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(100, 150, 255, 0.3);
}

#game-info button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease-out;
}

#game-info button:hover {
    background: linear-gradient(135deg, rgba(30, 140, 255, 0.8), rgba(20, 80, 180, 0.8));
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 25px rgba(0,0,0,0.5), 0 0 40px rgba(0, 100, 255, 0.4);
}

#game-info button:hover::before {
    opacity: 1;
    transform: scale(1);
}

#game-info button:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
}

/* Sidebar del tabellone */
#board-sidebar {
    grid-area: board-sidebar;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 15px;
    align-self: stretch;
    height: 100%;
    height: calc(100% - 2px); /* Esteso quasi fino al bordo inferiore */
    width: 50%;
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.4), rgba(10, 20, 40, 0.4));
    border-radius: 10px; /* Aggiunto bordo arrotondato anche in basso */
    padding: 12px;
    padding-bottom: 130px; /* Spazio ottimizzato per chat area coerente con design sidebar */
    box-shadow: inset 0 0 30px rgba(0,0,0,0.4), 0 5px 15px rgba(0,0,0,0.3);
    border: 1px solid rgba(50, 100, 200, 0.3);
    backdrop-filter: blur(5px);
    overflow: hidden;
    position: relative;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0 0 2px 0; /* Ridotto ulteriormente il margine inferiore */
    align-self: flex-start; /* Allinea all'inizio del contenitore */
}

#board-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M50 50c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10s-10-4.477-10-10 4.477-10 10-10zm10 8c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8zm40 40c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    border-radius: 10px; /* Aggiunto bordo arrotondato anche in basso */
    pointer-events: none;
    z-index: -1;
    opacity: 0.5;
    box-sizing: border-box;
}

/* Stili per i tab della sidebar */
.sidebar-tabs {
    display: flex;
    justify-content: space-between;
    background: linear-gradient(135deg, rgba(30, 60, 120, 0.8), rgba(15, 30, 60, 0.8));
    border-radius: 10px 10px 0 0;
    padding: 2px;
    margin: -12px -12px 10px -12px;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 100, 255, 0.2);
    position: sticky;
    top: -12px;
    z-index: 10;
    min-height: 70px; /* Assicura che ci sia abbastanza spazio per l'icona e il testo */
}

.sidebar-tabs .tab {
    flex: 1;
    text-align: center;
    padding: 10px 2px;
    color: rgba(200, 220, 255, 0.7);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-radius: 8px 8px 0 0;
    position: relative;
    overflow: hidden;
    font-size: 0.95em;
    text-shadow: 0 0 10px rgba(0, 100, 255, 0.3);
    letter-spacing: 0.5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
}

.sidebar-tabs .tab i {
    margin-bottom: 8px;
    font-size: 1.5em;
    color: rgba(100, 180, 255, 0.9);
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.5);
    transition: all 0.3s ease;
    display: block;
}

.sidebar-tabs .tab .tab-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
    font-size: 0.9em;
    line-height: 1.2;
}

.sidebar-tabs .tab::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(100, 180, 255, 0.8), transparent);
    transform: translateX(-50%);
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(100, 180, 255, 0.5);
}

.sidebar-tabs .tab:hover {
    color: rgba(220, 235, 255, 0.9);
    background: linear-gradient(to bottom, rgba(40, 80, 150, 0.3), rgba(30, 60, 120, 0.1));
}

.sidebar-tabs .tab:hover i {
    color: rgba(120, 200, 255, 1);
    text-shadow: 0 0 15px rgba(120, 200, 255, 0.7);
    transform: scale(1.1);
}

.sidebar-tabs .tab:hover::before {
    width: 70%;
}

.sidebar-tabs .tab.active {
    color: rgba(230, 245, 255, 1);
    background: linear-gradient(to bottom, rgba(50, 100, 180, 0.5), rgba(40, 80, 150, 0.3));
    box-shadow: inset 0 0 15px rgba(100, 180, 255, 0.2);
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.5);
}

.sidebar-tabs .tab.active i {
    color: rgba(150, 220, 255, 1);
    text-shadow: 0 0 15px rgba(150, 220, 255, 0.8);
}

/* Assicura che il testo non vada a capo anche in hover e active state */
.sidebar-tabs .tab:hover .tab-text,
.sidebar-tabs .tab.active .tab-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-tabs .tab.active::before {
    width: 90%;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(150, 220, 255, 0.9), transparent);
    box-shadow: 0 0 15px rgba(150, 220, 255, 0.7);
}

/* Stili per i contenuti dei tab */
#sidebar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%; /* Assicura che occupi tutta l'altezza disponibile */
}

.tab-content {
    display: none;
    flex: 1;
    flex-direction: column;
    animation: fadeIn 0.5s ease-in-out;
    overflow-y: auto;
    max-height: 100%;
}

.tab-content.active {
    display: flex;
    height: 100%; /* Assicura che occupi tutta l'altezza disponibile */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Stili per il tab Analisi */
.analisi-container {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-bottom: 15px;
}

.analisi-container h4 {
    margin: 0 0 15px 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.analisi-container h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
}

.analisi-stats {
    margin-bottom: 20px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(100, 150, 255, 0.2);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: rgba(200, 220, 255, 0.8);
    font-weight: 500;
}

.stat-value {
    color: rgba(230, 245, 255, 1);
    font-weight: 700;
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.5);
}

.analisi-grafico {
    margin-top: 20px;
}

.analisi-grafico h5 {
    margin: 0 0 10px 0;
    color: #e0f0ff;
    font-size: 1em;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.grafico-placeholder {
    height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(15, 30, 60, 0.5);
    border-radius: 8px;
    border: 1px dashed rgba(100, 150, 255, 0.3);
}

.grafico-placeholder i {
    font-size: 2.5em;
    color: rgba(100, 150, 255, 0.5);
    margin-bottom: 10px;
}

.grafico-placeholder p {
    color: rgba(200, 220, 255, 0.7);
    font-style: italic;
    margin: 0;
}

/* Stili per il tab Giocatori */
.giocatori-container {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-bottom: 15px;
}

.giocatori-container h4 {
    margin: 0 0 15px 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.giocatori-container h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
}

.giocatore-profilo {
    background: linear-gradient(145deg, rgba(30, 60, 120, 0.4), rgba(15, 30, 60, 0.4));
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 100, 255, 0.1);
    border: 1px solid rgba(100, 150, 255, 0.2);
    transition: all 0.3s ease;
}

.giocatore-profilo:last-child {
    margin-bottom: 0;
}

.giocatore-profilo:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border-color: rgba(100, 150, 255, 0.4);
}

.profilo-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.profilo-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(40, 80, 150, 0.5);
    margin-right: 12px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 100, 255, 0.3);
    border: 2px solid rgba(100, 150, 255, 0.4);
    background-size: cover;
    background-position: center;
}

.profilo-info {
    flex: 1;
}

.profilo-info h5 {
    margin: 0 0 5px 0;
    color: rgba(230, 245, 255, 1);
    font-size: 1.1em;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.profilo-livello {
    display: inline-block;
    padding: 3px 8px;
    background: linear-gradient(135deg, rgba(50, 100, 180, 0.6), rgba(30, 60, 120, 0.6));
    border-radius: 4px;
    font-size: 0.85em;
    color: rgba(220, 235, 255, 0.9);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 10px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
}

/* Stili per il tab Gioca */
.game-actions, .game-status {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-bottom: 8px;
}

.game-actions h4, .game-status h4 {
    margin: 0 0 15px 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.game-actions h4 i, .game-status h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.action-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 10px;
    background: linear-gradient(135deg, rgba(40, 80, 150, 0.6), rgba(20, 40, 80, 0.6));
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-radius: 8px;
    color: rgba(230, 245, 255, 1);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 100, 255, 0.1);
    position: relative; /* Per posizionare il contatore */
}

.action-button i {
    font-size: 1.8em;
    margin-bottom: 8px;
    color: rgba(150, 220, 255, 1);
    text-shadow: 0 0 15px rgba(150, 220, 255, 0.8);
    transition: all 0.3s ease;
}

.action-button:hover {
    transform: translateY(-3px);
    background: linear-gradient(135deg, rgba(50, 100, 180, 0.7), rgba(30, 60, 120, 0.7));
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
}

.action-button:hover i {
    transform: scale(1.1);
    color: rgba(180, 230, 255, 1);
    text-shadow: 0 0 20px rgba(180, 230, 255, 0.9);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 100, 255, 0.1);
}

/* Stili per il pulsante draw-card-button quando è disabilitato */
.action-button:disabled,
.action-button[disabled] {
    background: linear-gradient(135deg, rgba(60, 60, 60, 0.4), rgba(40, 40, 40, 0.4));
    border-color: rgba(80, 80, 80, 0.3);
    color: rgba(150, 150, 150, 0.7);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-button:disabled i,
.action-button[disabled] i {
    color: rgba(120, 120, 120, 0.7);
    text-shadow: none;
}

.action-button:disabled:hover,
.action-button[disabled]:hover {
    transform: none;
    background: linear-gradient(135deg, rgba(60, 60, 60, 0.4), rgba(40, 40, 40, 0.4));
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-button:disabled:hover i,
.action-button[disabled]:hover i {
    transform: none;
    color: rgba(120, 120, 120, 0.7);
}

/* Contatore carte rimanenti nel mazzo */
.deck-counter {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, rgba(255, 100, 50, 0.9), rgba(200, 50, 0, 0.9));
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

/* Animazione di highlight quando il contatore cambia */
@keyframes pulse-counter {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.deck-counter.highlight {
    animation: pulse-counter 0.5s ease;
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(100, 150, 255, 0.2);
}

.status-row:last-child {
    border-bottom: none;
}

.status-label {
    color: rgba(200, 220, 255, 0.8);
    font-weight: 500;
}

.status-value {
    color: rgba(230, 245, 255, 1);
    font-weight: 700;
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.5);
}

/* Stili per le opzioni di fine partita integrate nello stato partita */
.end-game-options-row {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(100, 150, 255, 0.3);
    display: flex;
    align-items: center;
}

.end-game-buttons-integrated {
    display: flex;
    gap: 10px;
    width: 100%;
    flex: 1;
    justify-content: flex-end;
}

.end-game-buttons-integrated .end-game-button {
    flex: 1;
    padding: 6px 10px;
    font-size: 0.85em;
    min-width: 0;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
}

.end-game-buttons-integrated .end-game-button i {
    margin-right: 5px;
    font-size: 1em;
}

/* Media query per schermi più piccoli */
@media (max-width: 1200px) {
    .end-game-buttons-integrated {
        flex-direction: column;
        gap: 8px;
    }
}

/* Navigazione mosse */
.move-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.move-navigation-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.move-nav-btn {
    background: linear-gradient(135deg, #2a4a8a 0%, #1a3a7a 100%);
    border: 1px solid #3a5a9a;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #ffffff;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.move-nav-btn:hover {
    background: linear-gradient(135deg, #3a5aaa 0%, #2a4a9a 100%);
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 15px rgba(100, 150, 255, 0.5);
}

.move-nav-btn:active {
    transform: scale(0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.move-nav-btn:disabled {
    background: linear-gradient(135deg, #4a4a5a 0%, #3a3a4a 100%);
    border-color: #5a5a6a;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.move-counter {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #ffffff;
    min-width: 40px;
    text-align: center;
    background: rgba(0, 0, 0, 0.2);
    padding: 3px 8px;
    border-radius: 10px;
}

/* Stili per gli highlight */
.card-highlight {
    animation: cardPulse 1.5s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.5);
    z-index: 10;
    transform: scale(1.05);
}

.cell-highlight {
    animation: cellPulse 1.5s ease-in-out infinite;
    box-shadow: inset 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.5);
    z-index: 5;
}

@keyframes cardPulse {
    0% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4); }
    50% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.9), 0 0 35px rgba(255, 215, 0, 0.7); }
    100% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4); }
}

@keyframes cellPulse {
    0% { box-shadow: inset 0 0 15px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4); }
    50% { box-shadow: inset 0 0 25px rgba(255, 215, 0, 0.9), 0 0 35px rgba(255, 215, 0, 0.7); }
    100% { box-shadow: inset 0 0 15px rgba(255, 215, 0, 0.6), 0 0 25px rgba(255, 215, 0, 0.4); }
}

@keyframes pulseHighlight {
    0% { color: rgba(230, 245, 255, 1); }
    50% { color: rgba(255, 215, 0, 1); text-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }
    100% { color: rgba(230, 245, 255, 1); }
}

/* Stili per i colori dei giocatori nel tab Gioca */
.white-player {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.black-player {
    color: #000000;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    background-color: rgba(200, 220, 255, 0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Stili per le opzioni di nuova partita */
.new-game-options {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-bottom: 15px;
}

.new-game-options h4 {
    margin: 0 0 15px 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.new-game-options h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.option-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-label {
    color: rgba(200, 220, 255, 0.8);
    font-weight: 500;
    flex: 1;
}

.option-value {
    flex: 2;
}

.game-select {
    width: 100%;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(30, 60, 120, 0.5), rgba(15, 30, 60, 0.5));
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-radius: 6px;
    color: rgba(230, 245, 255, 1);
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 100, 255, 0.1);
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.game-select:hover {
    background: linear-gradient(135deg, rgba(40, 80, 150, 0.6), rgba(20, 40, 80, 0.6));
    border-color: rgba(100, 150, 255, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 100, 255, 0.2);
}

.game-select:focus {
    outline: none;
    border-color: rgba(100, 180, 255, 0.7);
    box-shadow: 0 0 0 2px rgba(100, 180, 255, 0.3), 0 5px 15px rgba(0, 0, 0, 0.3);
}

.game-select option {
    background-color: rgba(15, 30, 60, 0.9);
    color: rgba(230, 245, 255, 1);
}

.start-game-button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
    margin-bottom: 15px;
    gap: 16px;
}

.start-game-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 18px 36px;
    width: 90%;
    max-width: 360px;
    margin: 0 auto;
    background: linear-gradient(135deg, rgba(60, 120, 200, 0.8), rgba(30, 60, 120, 0.8));
    border: 2px solid rgba(120, 180, 255, 0.6);
    border-radius: 12px;
    color: rgba(230, 245, 255, 1);
    font-weight: 700;
    font-size: 1.3em;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.7);
    letter-spacing: 0.5px;
    position: relative;
    border-bottom: 6px solid rgba(20, 40, 100, 0.9);
    transform: translateY(-2px);
}

.start-game-button i {
    margin-right: 15px;
    font-size: 1.4em;
    color: rgba(180, 230, 255, 1);
    text-shadow: 0 0 15px rgba(180, 230, 255, 0.9);
    transition: all 0.3s ease;
}

.start-game-button:hover {
    transform: translateY(-6px);
    background: linear-gradient(135deg, rgba(70, 140, 220, 0.9), rgba(50, 100, 180, 0.9));
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 120, 255, 0.4);
    border-color: rgba(150, 200, 255, 0.8);
    border-bottom-width: 6px;
}

.start-game-button:hover i {
    transform: scale(1.15) rotate(-5deg);
    color: rgba(200, 240, 255, 1);
    text-shadow: 0 0 25px rgba(200, 240, 255, 1);
}

.start-game-button:active {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 100, 255, 0.2);
    background: linear-gradient(135deg, rgba(80, 150, 230, 1), rgba(60, 110, 200, 1));
    border-bottom-width: 2px;
    margin-top: 4px;
}

/* Stile per il submenu del logo nella sidebar */
.logo-submenu {
    position: fixed; /* Posizionamento preciso */
    left: calc(var(--sidebar-width-collapsed, 60px) + 20px); /* Spostato ancora più a destra (20px) dal bordo della sidebar */
    top: 0;
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.95), rgba(10, 20, 40, 0.95));
    border-radius: 0 8px 8px 0;
    width: 240px; /* Leggermente aumentata per migliore leggibilità */
    box-shadow: 5px 0 25px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 100, 255, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.3);
    border-left: none; /* Rimuove il bordo sinistro per evitare linee doppie */
    z-index: 90; /* Più basso del menu principale per non sovrapporsi */
    opacity: 0;
    transform: translateX(-10px);
    pointer-events: none;
    transition: opacity 0.4s ease, transform 0.4s ease;
    overflow: hidden;
    height: 100vh; /* Copre tutta l'altezza come il menu laterale */
    display: flex;
    flex-direction: column;
    box-sizing: border-box; /* Assicura che padding e bordi siano inclusi nelle dimensioni */
    padding: 0; /* Reset padding per evitare spostamenti */
    text-align: left; /* Allineamento generale a sinistra */
}

/* Rimuovo completamente il vecchio stile e utilizzo un approccio JavaScript */
/* Il submenu sarà controllato tramite JavaScript per precisione */
.game-sidebar .logo-submenu.visible,
.game-sidebar .logo-submenu:hover {
    opacity: 1;
    transform: translateX(0);
    pointer-events: all;
    left: calc(var(--sidebar-width-collapsed, 60px) + 20px); /* Mantiene la stessa posizione durante l'hover */
}

/* Rimuovere la freccia indicatrice */

.logo-submenu ul {
    list-style: none;
    padding: 60px 0 10px 0; /* Maggiore padding in alto per posizionare le voci più in basso */
    margin: 0;
    width: 100%; /* Assicura che la lista occupi tutta la larghezza disponibile */
    text-align: left; /* Allineamento a sinistra generale */
}

.logo-submenu ul li {
    padding: 0;
    margin: 0;
    text-align: left;
    width: 100%;
}

.logo-submenu ul li a {
    display: flex;
    align-items: center;
    padding: 16px 10px; /* Ridotto ulteriormente il padding laterale sinistro */
    color: rgba(220, 235, 255, 0.9);
    text-decoration: none;
    font-size: 1.2em;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(100, 150, 255, 0.1);
    text-align: left; /* Forza l'allineamento a sinistra */
    justify-content: flex-start; /* Allinea i contenuti a sinistra */
    width: 100%; /* Assicura che l'intera larghezza sia utilizzata */
    box-sizing: border-box; /* Mantiene il padding entro i limiti del contenitore */
}

.logo-submenu ul li a:hover {
    background-color: rgba(100, 150, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.7);
    border-left: 4px solid rgba(100, 180, 255, 0.8);
    padding-left: 6px; /* Ridotto per mantenere l'allineamento con il nuovo padding di 10px */
}

.logo-submenu ul li a i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
    color: rgba(100, 180, 255, 0.9);
    font-size: 1.2em;
    min-width: 20px; /* Garantisce che le icone siano allineate */
    display: inline-block; /* Assicura che l'icona mantenga la sua dimensione */
}

.logo-submenu ul li a:hover i {
    color: rgba(150, 200, 255, 1);
    text-shadow: 0 0 10px rgba(150, 200, 255, 0.8);
}

/* Stile per il pulsante Sfida un Amico */
.secondary-game-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 30px;
    width: 80%;
    max-width: 320px;
    margin: 0 auto;
    background: linear-gradient(135deg, rgba(100, 100, 130, 0.6), rgba(60, 60, 80, 0.6));
    border: 2px solid rgba(150, 150, 200, 0.4);
    border-radius: 10px;
    color: rgba(200, 210, 230, 0.8);
    font-weight: 600;
    font-size: 1.1em;
    cursor: not-allowed;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 100, 150, 0.2);
    text-shadow: 0 0 8px rgba(150, 150, 200, 0.6);
    letter-spacing: 0.5px;
    position: relative;
    border-bottom: 4px solid rgba(50, 50, 70, 0.7);
    transform: translateY(-2px);
    opacity: 0.8;
}

.secondary-game-button::before {
    content: 'Solo per utenti registrati';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7em;
    color: rgba(200, 200, 230, 0.7);
    white-space: nowrap;
    text-shadow: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.secondary-game-button:hover::before {
    opacity: 1;
}

.secondary-game-button i {
    margin-right: 12px;
    font-size: 1.3em;
    color: rgba(150, 150, 200, 0.9);
    transition: all 0.3s ease;
}

.secondary-game-button:hover {
    transform: translateY(-4px);
    opacity: 0.9;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.35), 0 0 25px rgba(100, 100, 150, 0.3);
}

/* Stili per la sezione Skèmino notation */
#skemino-notation-area {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-top: 20px;
    margin-bottom: 0;
    height: 320px;
    max-height: 45vh;
    display: flex;
    flex-direction: column;
}

#skemino-notation-area h4 {
    margin: 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

#skemino-notation-area h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
    text-shadow: 0 0 10px rgba(100, 180, 255, 0.7);
}

/* Stili per il contenuto della notazione PSN */
.psn-text {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    line-height: 1.2;
    color: rgba(200, 220, 255, 0.9);
    white-space: pre-wrap;
    word-break: break-word;
    overflow-y: auto;
    flex: 1; /* Fa sì che l'elemento si espanda per occupare lo spazio disponibile */
    padding: 8px;
    margin-top: 10px;
    background: rgba(10, 20, 40, 0.4);
    border-radius: 6px;
    border: 1px solid rgba(100, 150, 255, 0.2);
    display: flex;
    flex-direction: column;
}

.psn-text .header {
    color: rgba(150, 200, 255, 1);
    font-weight: bold;
    margin-bottom: 5px;
}

.psn-text .setup {
    color: rgba(180, 220, 255, 0.9);
    margin-bottom: 5px;
}

.psn-text .moves {
    color: rgba(200, 230, 255, 0.9);
}

.psn-text .move-number {
    color: rgba(150, 200, 255, 0.8);
    font-weight: bold;
    margin-right: 2px;
}

.psn-text .white-move {
    color: rgba(255, 255, 255, 0.9);
    margin-right: 5px;
}

.psn-text .black-move {
    color: rgba(200, 200, 200, 0.9);
}

.psn-text .result {
    color: rgba(255, 215, 0, 0.9);
    font-weight: bold;
    margin-top: 5px;
}

/* Stili per le opzioni di fine partita */
.game-end-options {
    background: linear-gradient(145deg, rgba(20, 40, 80, 0.6), rgba(10, 20, 40, 0.6));
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    margin-top: 20px;
    margin-bottom: 15px;
}

.game-end-options h4 {
    margin: 0 0 15px 0;
    color: #e0f0ff;
    font-size: 1.1em;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

.game-end-options h4 i {
    margin-right: 8px;
    color: rgba(100, 180, 255, 0.9);
}

.end-game-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.end-game-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 15px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    color: #e0f0ff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.end-game-button i {
    margin-right: 10px;
    font-size: 1.1em;
}

.draw-button {
    background: linear-gradient(135deg, rgba(60, 120, 180, 0.8), rgba(40, 80, 150, 0.8));
    border: 1px solid rgba(80, 140, 200, 0.4);
}

.draw-button:hover {
    background: linear-gradient(135deg, rgba(70, 130, 190, 0.9), rgba(50, 90, 160, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(80, 140, 200, 0.3);
}

.resign-button {
    background: linear-gradient(135deg, rgba(180, 60, 60, 0.8), rgba(150, 40, 40, 0.8));
    border: 1px solid rgba(200, 80, 80, 0.4);
}

.resign-button:hover {
    background: linear-gradient(135deg, rgba(190, 70, 70, 0.9), rgba(160, 50, 50, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(200, 80, 80, 0.3);
}

.end-game-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 100, 255, 0.2);
}

/* Stili per le modali di conferma */
.game-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 99999; /* Valore di z-index molto alto per essere sopra tutto */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
    opacity: 1;
    transition: opacity 0.6s ease-in-out;
    will-change: opacity;
}

/* Stili per i popup di login e registrazione */
#login-popup .modal-content,
#register-popup .modal-content {
    max-width: 400px;
    background: linear-gradient(145deg, #1e3a6d, #102040);
    color: #fff;
    padding: 0;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    transform: translateY(0) scale(1);
    opacity: 1;
    transition: transform 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                opacity 0.7s ease-out;
    will-change: transform, opacity;
}

/* Stile rimosso perché potrebbe causare problemi */

#login-popup .modal-header,
#register-popup .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: rgba(0, 0, 0, 0.2);
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#login-popup .modal-header h4,
#register-popup .modal-header h4 {
    margin: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

#login-popup .modal-header .close-modal,
#register-popup .modal-header .close-modal {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.2s;
}

#login-popup .modal-header .close-modal:hover,
#register-popup .modal-header .close-modal:hover {
    color: #fff;
}

#login-popup .modal-body,
#register-popup .modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.form-group input {
    width: 100%;
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #000;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: rgba(82, 168, 236, 0.8);
    box-shadow: 0 0 8px rgba(82, 168, 236, 0.6);
    background-color: #fff;
    color: #000;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    margin: 15px 0;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 5px;
}

.remember-me input {
    margin: 0;
}

#popup-forgot-password {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

#popup-forgot-password:hover {
    text-decoration: underline;
    color: #fff;
}

.error-message {
    color: #ff6b6b;
    font-size: 0.9rem;
    margin: 10px 0;
    text-align: center;
}

.terms-container {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 15px;
    font-size: 0.85rem;
}

#login-popup .modal-footer,
#register-popup .modal-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

#popup-login-button,
#popup-register-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(to right, #3498db, #2980b9);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s, transform 0.1s;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

#popup-login-button:hover,
#popup-register-button:hover {
    background: linear-gradient(to right, #3cb0fd, #3498db);
    transform: translateY(-1px);
}

#popup-login-button:active,
#popup-register-button:active {
    transform: translateY(1px);
}

.register-link, .login-link {
    margin-top: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.register-link a, .login-link a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

.register-link a:hover, .login-link a:hover {
    text-decoration: underline;
}

.modal-content {
    background: linear-gradient(145deg, rgba(30, 60, 120, 0.95), rgba(15, 30, 60, 0.95));
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 100, 255, 0.3);
    border: 2px solid rgba(100, 150, 255, 0.4);
    overflow: hidden;
    animation: modalAppear 0.3s ease-out;
}

@keyframes modalAppear {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(40, 80, 150, 0.8), rgba(20, 40, 80, 0.8));
    border-bottom: 1px solid rgba(100, 150, 255, 0.3);
}

.modal-header h4 {
    margin: 0;
    color: #e0f0ff;
    font-size: 1.2em;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
    display: flex;
    align-items: center;
}

.modal-header h4 i {
    margin-right: 10px;
    font-size: 1.1em;
}

.modal-body {
    padding: 20px;
    color: #e0f0ff;
}

.modal-body p {
    margin: 0 0 15px 0;
    font-size: 1.05em;
}

.modal-note {
    font-size: 0.9em !important;
    color: rgba(200, 220, 255, 0.8);
    font-style: italic;
}

.modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    border-top: 1px solid rgba(100, 150, 255, 0.2);
    background: rgba(20, 40, 80, 0.5);
}

.confirm-button, .cancel-button {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.confirm-button {
    background: linear-gradient(135deg, rgba(50, 180, 100, 0.8), rgba(30, 150, 80, 0.8));
    color: #e0f0ff;
    border: 1px solid rgba(70, 200, 120, 0.4);
}

.confirm-button:hover {
    background: linear-gradient(135deg, rgba(60, 190, 110, 0.9), rgba(40, 160, 90, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(50, 180, 100, 0.3);
}

.cancel-button {
    background: linear-gradient(135deg, rgba(100, 120, 150, 0.6), rgba(80, 100, 130, 0.6));
    color: #e0f0ff;
    border: 1px solid rgba(120, 140, 170, 0.4);
}

.cancel-button:hover {
    background: linear-gradient(135deg, rgba(110, 130, 160, 0.7), rgba(90, 110, 140, 0.7));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.confirm-button:active, .cancel-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Indicatore di vantaggio */
#advantage-indicator {
    grid-area: advantage;
    display: flex;
    flex-direction: column;
    height: 100% !important; /* Altezza completa per coprire tutto il tabellone */
    width: 30px;
    border-radius: 0; /* Rimosso border-radius */
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.5), 0 0 15px rgba(0,0,0,0.3);
    border: 1px solid rgba(30, 60, 100, 0.5);
    position: relative;
    margin-right: 0px;
    z-index: 10; /* Assicura che l'indicatore sia sopra altri elementi */
    margin-bottom: 0 !important; /* Rimosso margine inferiore */
    padding: 0 !important; /* Assicura che non ci sia padding */
}

/* Divisore centrale per l'indicatore di vantaggio */
#advantage-indicator::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    height: 3px;
    background: linear-gradient(to right, rgba(100, 150, 255, 0.2), rgba(150, 180, 220, 0.9), rgba(100, 150, 255, 0.2));
    box-shadow: 0 0 5px rgba(100, 150, 255, 0.7), 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 20; /* Assicura che sia sopra tutti gli altri elementi dell'indicatore */
    transform: translateY(-50%);
    pointer-events: none; /* Permette di cliccare attraverso il divisore */
    border-top: 1px solid rgba(255, 255, 255, 0.4);
    border-bottom: 1px solid rgba(0, 0, 0, 0.4);
}

.advantage-white {
    width: 100%;
    height: 50%; /* Altezza predefinita 50% */
    background: linear-gradient(to bottom, rgba(255,255,255,0.9), rgba(200,220,255,0.8));
    transition: height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1); /* Transizione più fluida */
    position: relative;
    box-shadow: inset 0 0 10px rgba(255,255,255,0.5);
    border-bottom: 1px solid rgba(0,0,0,0.2);
    will-change: height; /* Ottimizzazione per le animazioni */
    min-height: 0%; /* Permette di arrivare a 0% durante una vittoria */
    max-height: 100%; /* Permette di arrivare a 100% durante una vittoria */
    flex-shrink: 0; /* Impedisce la compressione */
}

.advantage-white::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.4), rgba(255,255,255,0.1));
    animation: shine 3s infinite;
    z-index: 1;
}

.advantage-label {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px; /* Ridotto il font-size da 25px a 16px per adattare meglio i dadi G1 e G2 */
    font-weight: bold;
    z-index: 2;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
}

.advantage-white .advantage-label {
    bottom: 6px; /* Ridotto da 10px a 6px per adattarsi al font-size più piccolo */
    color: #000;
}

.advantage-black .advantage-label {
    top: 6px; /* Ridotto da 10px a 6px per adattarsi al font-size più piccolo */
    color: #fff;
}

.advantage-black {
    width: 100%;
    height: 50%; /* Altezza predefinita 50% */
    background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(40,40,40,0.8));
    transition: height 0.5s cubic-bezier(0.25, 0.1, 0.25, 1); /* Transizione più fluida */
    position: relative;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.5);
    border-top: 1px solid rgba(255,255,255,0.2);
    will-change: height; /* Ottimizzazione per le animazioni */
    min-height: 0%; /* Permette di arrivare a 0% durante una vittoria */
    max-height: 100%; /* Permette di arrivare a 100% durante una vittoria */
    flex-shrink: 0; /* Impedisce la compressione */
}

.advantage-black::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(40,40,40,0.4), rgba(0,0,0,0.1));
    animation: shine 3s infinite reverse;
    z-index: 1;
}

@keyframes shine {
    0% { opacity: 0.3; }
    50% { opacity: 0.7; }
    100% { opacity: 0.3; }
}

/* Effetti per vantaggio significativo */
.advantage-white.significant-advantage .advantage-label,
.advantage-black.significant-advantage .advantage-label {
    animation: pulse 1.5s infinite;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.advantage-white.significant-advantage {
    background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(200,220,255,0.9));
    box-shadow: inset 0 0 15px rgba(255,255,255,0.8);
}

.advantage-black.significant-advantage {
    background: linear-gradient(to top, rgba(0,0,0,1), rgba(40,40,40,0.9));
    box-shadow: inset 0 0 15px rgba(0,0,0,0.8);
}

/* Effetto speciale per il ribaltone */
.advantage-white.ribaltone-possible {
    background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(200,220,255,0.9), rgba(100,150,255,0.8));
    box-shadow: inset 0 0 20px rgba(100,150,255,0.9);
    animation: ribaltone-pulse 2s infinite;
}

.advantage-black.ribaltone-possible {
    background: linear-gradient(to top, rgba(0,0,0,1), rgba(40,40,40,0.9), rgba(100,100,100,0.8));
    box-shadow: inset 0 0 20px rgba(100,100,100,0.9);
    animation: ribaltone-pulse 2s infinite;
}

@keyframes ribaltone-pulse {
    0% { box-shadow: inset 0 0 20px rgba(100,150,255,0.9); }
    50% { box-shadow: inset 0 0 30px rgba(100,150,255,1); }
    100% { box-shadow: inset 0 0 20px rgba(100,150,255,0.9); }
}

/* Effetto speciale per la vittoria per ribaltone */
.advantage-white.ribaltone-victory {
    background: linear-gradient(to bottom, rgba(255,255,255,1), rgba(200,220,255,0.9), rgba(100,150,255,0.8));
    box-shadow: inset 0 0 30px rgba(100,150,255,1);
    animation: ribaltone-victory 1s infinite, victoryGlow 1.5s infinite alternate;
    height: 100% !important; /* Forza l'altezza per mostrare la vittoria completa */
}

.advantage-black.ribaltone-victory {
    background: linear-gradient(to top, rgba(0,0,0,1), rgba(40,40,40,0.9), rgba(100,100,100,0.8));
    box-shadow: inset 0 0 30px rgba(100,100,100,1);
    animation: ribaltone-victory 1s infinite, victoryGlow 1.5s infinite alternate;
    height: 100% !important; /* Forza l'altezza per mostrare la vittoria completa */
}

@keyframes ribaltone-victory {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Animazione per il brillare dell'indicatore di vantaggio durante la vittoria */
@keyframes victoryGlow {
    0% { box-shadow: inset 0 0 15px rgba(255, 215, 0, 0.7), 0 0 20px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: inset 0 0 30px rgba(255, 215, 0, 1), 0 0 40px rgba(255, 215, 0, 0.8); }
    100% { box-shadow: inset 0 0 15px rgba(255, 215, 0, 0.7), 0 0 20px rgba(255, 215, 0, 0.5); }
}

@keyframes pulse {
    0% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.2); }
    100% { transform: translateX(-50%) scale(1); }
}

/* Classi per la vittoria nell'indicatore di vantaggio - con !important per forzare la priorità */
#advantage-indicator.white-wins .advantage-white {
    height: 100% !important;
    animation: victoryGlow 1.5s infinite alternate;
    border-bottom: none !important; /* Rimuove il bordo quando occupa tutto lo spazio */
}

#advantage-indicator.white-wins .advantage-black {
    height: 0% !important;
    border-top: none !important; /* Rimuove il bordo quando occupa 0 spazio */
}

#advantage-indicator.black-wins .advantage-white {
    height: 0% !important;
    border-bottom: none !important; /* Rimuove il bordo quando occupa 0 spazio */
}

#advantage-indicator.black-wins .advantage-black {
    height: 100% !important;
    animation: victoryGlow 1.5s infinite alternate;
    border-top: none !important; /* Rimuove il bordo quando occupa tutto lo spazio */
}

/* Tabellone */
#game-board {
    grid-area: game-board;
    margin: 0 auto;
    display: grid;
    width: 100%;
    height: 100%;

    /* Variabili base per le dimensioni delle celle */
    --cell-width: var(--cell-base-width);
    --cell-height: calc(var(--cell-width) * 1.46);

    /* Grid con proporzioni uniformi */
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, minmax(0, 1fr));
    gap: 5px;

    /* Estetica base */
    border: 2px solid rgba(10, 20, 40, 0.9);
    border-radius: var(--card-border-radius);
    position: relative;
    background: linear-gradient(135deg, #0a1520, #162736);
    box-shadow: 0 20px 50px rgba(0,0,0,0.7), 0 0 60px rgba(0, 100, 255, 0.25), inset 0 0 40px rgba(0, 30, 80, 0.5);
    padding: 8px;

    /* Posizionamento e comportamento */
    overflow: hidden;
    align-self: center;
    justify-self: center;
    animation: boardAppear 1.2s cubic-bezier(0.2, 0.8, 0.2, 1.2);
    transform-style: preserve-3d;
    perspective: 1200px;
    z-index: var(--z-index-default);

    /* Responsive sizing */
    max-width: 100%;
    max-height: 100%;
    aspect-ratio: 1;
    box-sizing: border-box;
}

/* Large Desktop / Monitor grandi - RIMOSSO per evitare conflitti con la media query specifica per 1920px */
/* Tutte le regole per 1920px sono ora definite nella media query specifica per 1920x1080 */







/* Extra-large devices (17" notebooks / small monitors) */
@media (min-width: 1600px) and (max-width: 1920px) and (min-height: 900px) and (max-height: 1080px) {
    /* Dimensioni ottimizzate per schermi 17" */
    .card-slot, .card {
        /* Le dimensioni delle carte usano ora le variabili CSS */
        width: var(--card-base-width) !important;
        height: var(--card-base-height) !important;
        margin: var(--space-xxs) !important;
        border-radius: var(--card-border-radius) !important;
    }

    .hand-area {
        grid-template-columns: repeat(5, minmax(var(--card-base-width), 1fr)) !important;
        gap: var(--space-xxs) !important;
        padding: var(--space-xs) !important;
        min-height: calc(var(--card-base-height) * 2 + var(--space-xl)) !important;
    }

    /* Riduzione dell'effetto hover per evitare sovrapposizioni */
    .card:hover {
        transform: translateY(-8px) scale(1.08) rotateY(5deg) !important;
    }

    /* Riduzione dell'effetto di selezione */
    .hand-area .card-slot .card.selected {
        transform: scale(1.15) !important;
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(50, 220, 100, 0.6) !important; /* Aggiunta ombra per coerenza con altre regole */
        border: 2px solid rgba(50, 220, 100, 0.8) !important; /* Aggiunto bordo per coerenza */
        z-index: 20 !important; /* Aggiunto z-index per coerenza */
    }
}





#game-board::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, rgba(0, 100, 255, 0.2), rgba(0, 50, 120, 0.2));
    border-radius: 15px;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.8;
    animation: boardGlow 4s ease-in-out infinite alternate;
}

/* Sistema unificato di media queries per il game-board */

/* Extra-small devices (phones, 480px and down) */
@media (max-width: 480px) {
    #game-board {
        --cell-width: min(5vw, 22vh, 85px);
        --cell-height: calc(var(--cell-width) * 1.46);
        transform: scale(0.95);
        padding: 6px;
        gap: 2px;
        border: 2px solid rgba(10, 20, 40, 0.9);
        height: auto;
        width: 100%;
        margin: auto;
        align-self: center;
        justify-self: center;
        overflow: hidden;
    }
}

/* Small devices (tablets, 481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    #game-board {
        --cell-width: min(5.5vw, 23vh, 100px);
        --cell-height: calc(var(--cell-width) * 1.46);
        padding: min(1vw, 15px);
        gap: min(0.2vw, 2px);
        border: min(0.25vw, 4px) solid rgba(10, 20, 40, 0.9);
        height: 95%;
        width: 95%;
        margin: auto;
        align-self: center;
        justify-self: center;
    }
}

/* Medium devices (tablets, laptops, 769px to 992px) */
@media (min-width: 769px) and (max-width: 992px) {
    #game-board {
        --cell-width: min(5.8vw, 24vh, 110px);
        --cell-height: calc(var(--cell-width) * 1.46);
        padding: min(1.2vw, 18px);
        gap: min(0.22vw, 3px);
        border: min(0.3vw, 5px) solid rgba(10, 20, 40, 0.9);
        width: 98%;
        height: 98%;
        margin: auto;
        align-self: center;
        justify-self: center;
    }
}

/* Desktop devices (993px to 1200px) */
@media (min-width: 993px) and (max-width: 1200px) {
    #game-board {
        --cell-width: min(6vw, 25vh, 120px);
        --cell-height: calc(var(--cell-width) * 1.46);
        width: 98%;
        height: 98%;
        transform: none;
        margin: auto;
        padding: min(1.4vw, 20px);
        gap: min(0.25vw, 3px);
        background: radial-gradient(circle at center, rgba(30, 60, 100, 0.5), rgba(10, 30, 60, 0.8));
        border: min(0.35vw, 5px) solid rgba(10, 20, 40, 0.9);
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 100, 255, 0.3);
    }
}

/* Large desktop (1201px to 1600px) */
@media (min-width: 1201px) and (max-width: 1600px) {
    #game-board {
        --cell-width: min(6.2vw, 27vh, 130px);
        --cell-height: calc(var(--cell-width) * 1.46);
        width: 98%;
        height: 98%;
        transform: none;
        margin: auto;
        padding: min(1.6vw, 24px);
        gap: min(0.28vw, 4px);
        background: radial-gradient(circle at center, rgba(30, 60, 100, 0.5), rgba(10, 30, 60, 0.8));
        border: min(0.38vw, 5.5px) solid rgba(10, 20, 40, 0.9);
        border-radius: 11px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 100, 255, 0.3);
    }
}

/* XL desktop (1601px to 1920px) */
@media (min-width: 1601px) and (max-width: 1920px) {
    #game-board {
        --cell-width: min(6.5vw, 28vh, 135px);
        --cell-height: calc(var(--cell-width) * 1.46);
        width: 98%;
        height: 98%;
        transform: none;
        margin: auto;
        padding: min(1.8vw, 26px);
        gap: min(0.3vw, 5px);
        background: radial-gradient(circle at center, rgba(30, 60, 100, 0.5), rgba(10, 30, 60, 0.8));
        border: min(0.4vw, 6px) solid rgba(10, 20, 40, 0.9);
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 100, 255, 0.3);
    }
}

/* XXL desktop (1921px and up) */
@media (min-width: 1921px) {
    #game-board {
        --cell-width: min(7vw, 29vh, 145px);
        --cell-height: calc(var(--cell-width) * 1.46);
        width: 98%;
        height: 98%;
        transform: none;
        margin: auto;
        padding: min(2vw, 28px);
        gap: min(0.35vw, 6px);
        background: radial-gradient(circle at center, rgba(30, 60, 100, 0.5), rgba(10, 30, 60, 0.8));
        border: min(0.45vw, 7px) solid rgba(10, 20, 40, 0.9);
        border-radius: 14px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 100, 255, 0.3);
    }
}

    /* Garantisci che le notazioni delle celle mantengano la stessa dimensione a qualunque zoom */
    .cell::after {
        font-size: 12px !important; /* Dimensione fissa non influenzata dallo zoom */
        padding: 2px 4px !important; /* Padding fisso */
        background-color: rgba(30, 60, 120, 0.85) !important;
        color: rgba(230, 245, 255, 1) !important;
        border-radius: 3px !important;
        opacity: 0.9 !important; /* Sempre visibile */
        display: block !important;
        transform: none !important; /* Nessuna trasformazione che potrebbe alterarne la dimensione */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        z-index: 100 !important; /* Sempre in primo piano */
    }


    #player1-hand .card-value,
    #player2-hand .card-value {
        font-size: 20px !important;
    }

    #player1-hand .card-suit,
    #player2-hand .card-suit {
        font-size: 24px !important;
    }

    #board-area {
        display: flex;
        align-items: stretch;
        justify-content: center;
    }

    /* Container for game board */
    #board-area {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: stretch;
    }

@keyframes boardGlow {
    0% { opacity: 0.5; filter: blur(10px); }
    100% { opacity: 0.8; filter: blur(15px); }
}

@keyframes boardAppear {
    0% { opacity: 0; transform: translateY(30px) scale(0.9) rotateX(10deg); }
    50% { opacity: 1; transform: translateY(-10px) scale(1.02) rotateX(-5deg); }
    100% { opacity: 1; transform: translateY(0) scale(1) rotateX(0); }
}

/* Celle del tabellone */
.cell {
    background-color: rgba(240, 245, 255, 0.9);
    border-radius: 6px; /* Ridotto per rendere le celle più spigolose */
    transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
    cursor: pointer;
    box-shadow: inset 0 0 20px rgba(50,60,70,0.5), 0 8px 20px rgba(0,0,0,0.3), 0 0 30px rgba(0, 100, 255, 0.15);
    display: flex;
    justify-content: center;
    transform-style: preserve-3d;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 1px; /* Ulteriormente ridotto */
    border: 1px solid rgba(30, 60, 100, 0.4); /* Ulteriormente ridotto */
    backdrop-filter: blur(2px);
    transform: translateZ(0);
    min-width: 0;
    min-height: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

/* Gradiente per celle normali (non centrali) */
.cell:not([data-notation="c3"]):not([data-notation="c4"]):not([data-notation="d3"]):not([data-notation="d4"])::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.5) 0%, rgba(220,230,245,0.3) 40%, rgba(200,210,230,0.1) 70%, rgba(180,190,210,0) 100%);
    border-radius: 4px;
    z-index: 1;
    pointer-events: none;
}

/* Celle centrali rotte */
.cell[data-notation="c3"],
.cell[data-notation="c4"],
.cell[data-notation="d3"],
.cell[data-notation="d4"] {
    background-color: rgba(0, 0, 0, 0.9);
    position: relative;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(0,0,0,0.6), 0 5px 15px rgba(0,0,0,0.4), 0 0 30px rgba(0, 50, 100, 0.2);
    border: 2px solid rgba(20, 40, 80, 0.6);
}

/* Triangoli colorati del rombo centrale */
.cell[data-notation="c3"]::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(125deg, rgba(30, 120, 255, 0.9) 50%, transparent 40%);
    z-index: 1;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: drop-shadow(0 0 10px rgba(30, 120, 255, 0.5));
    pointer-events: none;
}

.cell[data-notation="d3"]::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(235deg, rgba(50, 220, 100, 0.9) 50%, transparent 50%);
    z-index: 1;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: drop-shadow(0 0 10px rgba(50, 220, 100, 0.5));
    pointer-events: none;
}

.cell[data-notation="c4"]::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(55deg, rgba(255, 60, 60, 0.9) 50%, transparent 50%);
    z-index: 1;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: drop-shadow(0 0 10px rgba(255, 60, 60, 0.5));
    pointer-events: none;
}

.cell[data-notation="d4"]::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(305deg, rgba(255, 220, 50, 0.9) 50%, transparent 50%);
    z-index: 1;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: drop-shadow(0 0 10px rgba(255, 220, 50, 0.5));
    pointer-events: none;
}

/* Notazioni per le celle centrali */
.cell[data-notation="c3"]::after,
.cell[data-notation="c4"]::after,
.cell[data-notation="d3"]::after,
.cell[data-notation="d4"]::after {
    content: attr(data-notation);
    position: absolute;
    bottom: 3px;
    right: 3px;
    font-size: 12px; /* Dimensione fissa in pixel uguale alle altre celle */
    line-height: 1;
    color: rgba(220, 235, 255, 0.95);
    font-weight: bold;
    pointer-events: none;
    z-index: 3;
    background-color: rgba(20, 40, 80, 0.75);
    padding: 3px 6px;
    border-radius: 6px;
    opacity: 0.9;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25), 0 0 10px rgba(30, 120, 255, 0.3);
    text-shadow: 0 0 5px rgba(30, 120, 255, 0.6);
    border: 1px solid rgba(120, 170, 255, 0.4);
    backdrop-filter: blur(2px);
}

/* Effetto hover per le notazioni delle celle centrali */
.cell[data-notation="c3"]:hover::after,
.cell[data-notation="c4"]:hover::after,
.cell[data-notation="d3"]:hover::after,
.cell[data-notation="d4"]:hover::after {
    opacity: 1;
    transform: scale(1.15) translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.35), 0 0 15px rgba(30, 120, 255, 0.5);
    background-color: rgba(30, 60, 120, 0.85);
    color: rgba(230, 245, 255, 1);
    z-index: 10;
}

/* Effetto hover migliorato senza animazioni che causano flash */
.cell[data-notation="c3"]:hover::before,
.cell[data-notation="d3"]:hover::before,
.cell[data-notation="c4"]:hover::before,
.cell[data-notation="d4"]:hover::before {
    filter: brightness(1.3) drop-shadow(0 0 15px rgba(255, 255, 255, 0.7));
    transform: scale(1.05);
}

.cell[data-notation="c3"]:hover {
    box-shadow: 0 0 20px rgba(30, 120, 255, 0.7), inset 0 0 15px rgba(30, 120, 255, 0.4);
    transform: scale(1.05) translateZ(10px);
    z-index: 5;
}

.cell[data-notation="d3"]:hover {
    box-shadow: 0 0 20px rgba(50, 220, 100, 0.7), inset 0 0 15px rgba(50, 220, 100, 0.4);
    transform: scale(1.05) translateZ(10px);
    z-index: 5;
}

.cell[data-notation="c4"]:hover {
    box-shadow: 0 0 20px rgba(255, 60, 60, 0.7), inset 0 0 15px rgba(255, 60, 60, 0.4);
    transform: scale(1.05) translateZ(10px);
    z-index: 5;
}

.cell[data-notation="d4"]:hover {
    box-shadow: 0 0 20px rgba(255, 220, 50, 0.7), inset 0 0 15px rgba(255, 220, 50, 0.4);
    transform: scale(1.05) translateZ(10px);
    z-index: 5;
}

/* Notazioni all'interno delle celle */
.cell::after {
    content: attr(data-notation);
    position: absolute;
    bottom: 3px;
    right: 3px;
    font-size: 0.85em;
    /* Assicura che le notazioni mantengano dimensioni consistenti a qualsiasi zoom */
    font-size: 12px; /* Dimensione fissa in pixel */
    line-height: 1;
    color: rgba(220, 235, 255, 0.95);
    font-weight: bold;
    pointer-events: none;
    z-index: 2;
    background-color: rgba(20, 40, 80, 0.75);
    padding: 3px 6px;
    border-radius: 6px;
    opacity: 0.9;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25), 0 0 10px rgba(30, 120, 255, 0.3);
    text-shadow: 0 0 5px rgba(30, 120, 255, 0.6);
    border: 1px solid rgba(120, 170, 255, 0.4);
    backdrop-filter: blur(2px);
}

/* Hover effects will be applied only when boardInteractionEnabled is true */
body.board-interaction-enabled .cell:hover::after {
    opacity: 1;
    transform: scale(1.15) translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.35), 0 0 15px rgba(30, 120, 255, 0.5);
    background-color: rgba(30, 60, 120, 0.85);
    color: rgba(230, 245, 255, 1);
    font-size: 12px; /* Mantiene dimensione fissa anche in hover */
}

body.board-interaction-enabled .cell:hover {
    background-color: rgba(220, 240, 255, 0.95);
    box-shadow: 0 0 12px 4px rgba(135, 206, 250, 0.6); /* Esempio: bagliore azzurro chiaro */
    z-index: 10;
    border-color: rgba(100, 150, 255, 0.6);
    /* Aggiunta una transizione più specifica per il box-shadow se si vuole un effetto smooth */
    transition: background-color 0.3s cubic-bezier(0.19, 1, 0.22, 1), border-color 0.3s cubic-bezier(0.19, 1, 0.22, 1), box-shadow 0.3s ease-out;
}

body.board-interaction-enabled .cell:not([data-notation="c3"]):not([data-notation="c4"]):not([data-notation="d3"]):not([data-notation="d4"]):hover::before {
    background: radial-gradient(circle at center, rgba(255,255,255,0.6) 0%, rgba(220,230,245,0.4) 40%, rgba(200,210,230,0.2) 70%, rgba(180,190,210,0) 100%);
}

/* When board interaction is disabled */
body:not(.board-interaction-enabled) .cell {
    pointer-events: none;
}

/* Area delle carte in mano */
.hand-area {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(5, minmax(var(--card-base-width), 1fr)); /* Usa la variabile CSS per la larghezza */
    grid-template-rows: repeat(2, auto); /* 2 righe che si adattano al contenuto */
    justify-items: center;
    align-items: center;
    gap: var(--space-xxs); /* Spazio minimo consistente */
    padding: var(--space-xs); /* Padding responsive */
    background-color: rgba(10, 20, 40, 0.5);
    border-radius: calc(var(--card-border-radius) * 1.5); /* Bordo proporzionale */
    transition: none;
    box-shadow: inset 0 0 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 100, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.2); /* Ridotto per guadagnare spazio */
    position: relative;
    z-index: 1;
    min-height: 0; /* Rimosso valore fisso per adattarsi meglio al contenitore */
    max-height: 100%; /* Assicura che non superi mai il contenitore */
    overflow: hidden; /* Previene overflow */
    box-sizing: border-box; /* Assicura che padding sia incluso nella larghezza */
    margin-top: 10px; /* Aggiunto margine superiore per compensare la rimozione degli elementi info */
}

.hand-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    border-radius: 12px;
    opacity: 0.3;
    pointer-events: none;
    z-index: -1;
}

.card-slot {
    width: var(--card-base-width); /* Usa la variabile CSS per larghezza consistente */
    height: var(--card-base-height); /* Mantiene l'aspetto 1.46:1 */
    background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9));
    border-radius: var(--card-border-radius); /* Usa la variabile CSS */
    display: flex;
    justify-content: center;
    align-items: center;
    margin: var(--space-xxs); /* Spazio minimo consistente */
    box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3);
    transition: none;
    border: 1px solid rgba(100, 150, 255, 0.3);
    transform: translateZ(0);
    box-sizing: border-box; /* Assicura che border sia incluso nella larghezza */
    position: relative;
    overflow: hidden;
    /* Assicura che il contenuto sia nascosto prima dell'animazione */
    isolation: isolate;
}

.card-slot:hover {
    /* Hover temporaneamente disabilitato per evitare flash */
}

.card-slot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 80%);
    z-index: 1;
    pointer-events: none;
}

.card-slot .card {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Quando la carta è nel tabellone, deve occupare tutta la cella */
.cell .card.board-card {
    width: 100% !important;
    height: 100% !important;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    border-radius: 5px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 20; /* Aumentato per essere sempre sopra l'hover delle celle */
    transform: translateZ(1px);
}

.cell .card.board-card:hover {
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 12px 30px rgba(0,0,0,0.4), 0 0 40px rgba(0, 100, 255, 0.3);
    z-index: 25; /* Aumentato ulteriormente quando in hover */
}

.cell .card.board-card img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* Manteniamo contain per evitare distorsioni, ma ora occuperà tutto lo spazio */
}

.card {
    background-color: transparent;
    width: 90px; /* Ridotto ulteriormente per evitare overflow */
    height: 131px; /* Ridotto proporzionalmente (90 * 1.46) */
    border-radius: 5px; /* Adattato alle nuove dimensioni */
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    user-select: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 15px rgba(0, 100, 255, 0.25); /* Ridotto per guadagnare spazio */
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    transform-style: preserve-3d;
    backface-visibility: hidden;
    animation: cardAppear 0.7s cubic-bezier(0.2, 0.8, 0.2, 1.2);
    overflow: hidden;
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 5px;
}

/* Rimuoviamo il gradient since ora usiamo immagini PNG */
.card::before {
    display: none;
}

@keyframes cardAppear {
    0% { opacity: 0; transform: scale(0.8) translateY(20px) rotateX(20deg); }
    60% { transform: scale(1.1) translateY(-10px) rotateX(-10deg); }
    80% { transform: scale(0.95) translateY(5px) rotateX(5deg); }
    100% { opacity: 1; transform: scale(1) translateY(0) rotateX(0); }
}

/* DISABILITAZIONE ANIMAZIONI PER CARTE AVVERSARIO IN MODALITÀ ONLINE */
.cell .card.opponent-card {
    animation: none !important;
    transition: none !important;
}

.cell .card.opponent-card.card-placed {
    animation: none !important;
    transition: none !important;
}

.card.opponent-card {
    animation: none !important;
    transition: none !important;
}

.card.opponent-card.card-placed {
    animation: none !important;
    transition: none !important;
}

.card:hover {
    transform: translateY(-12px) scale(1.12) rotateY(8deg);
    box-shadow: 0 15px 30px rgba(0,0,0,0.4), 0 0 50px rgba(0, 100, 255, 0.3);
    z-index: 20;
}

/* Le classi colore ora sono usate solo per gli effetti ma non cambiano l'aspetto */
.card.rock, .card.paper, .card.scissors {
    background: transparent;
    border: none;
}

/* Definizione base per le carte nascoste */
.card.hidden {
    background-image: url('img/carte/card-back.webp');
    background-color: #1a3a6a; /* Colore di fallback */
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #0f1c2a;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.card.hidden:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
}

/* Stile specifico per le carte nell'animazione di distribuzione */
.card.hidden.dealing-card {
    background-image: url('img/carte/card-back.webp') !important;
    background-color: #1a3a6a !important; /* Colore di fallback */
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

.dealing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 60%);
    border-radius: 8px;
    z-index: 1;
    pointer-events: none;
}

/* Stili per le particelle durante la distribuzione delle carte */
.card-deal-particles {
    position: absolute;
    width: 0;
    height: 0;
    z-index: 150;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 10px 5px rgba(100, 150, 255, 0.6);
    transform: translate(-50%, -50%);
}

/* Stile specifico per la carta iniziale nell'animazione */
.card.hidden.initial-card-animation {
    background-image: url('img/carte/card-back.webp');
    background-color: #1a3a6a; /* Colore di fallback */
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #0f1c2a;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* Area log di gioco rimossa */

/* Stile del log rimosso */

/* Stile del titolo del log rimosso */

/* Stile dell'after del titolo del log rimosso */

/* Stile della lista del log rimossa */

/* Stile della scrollbar del log rimossa */

/* Stile degli elementi della lista del log rimossi */

/* Animazione degli elementi del log rimossa */

/* Stile hover degli elementi del log rimossi */

/* Stile before degli elementi del log rimossi */

/* Stile dell'ultimo elemento del log e animazione rimossi */

/* Stili specifici per i tipi di elementi del log rimossi */

/* Evidenziazione del turno corrente */
#player1-area.current-turn, #player2-area.current-turn {
    background-color: rgba(20, 60, 40, 0.8);
    box-shadow: 0 8px 25px rgba(0,0,0,0.5), 0 0 40px rgba(50, 220, 100, 0.4), inset 0 0 20px rgba(50, 220, 100, 0.2);
    border: 3px solid rgba(50, 220, 100, 0.6);
    position: relative;
    z-index: 10;
}

/* Effetto di scala diverso per player1 e player2 */
#player1-area.current-turn, #player2-area.current-turn {
    transform: translateZ(5px); /* Mantiene solo un leggero effetto 3D senza scala */
}

/* Solo per il player2-area quando è il suo turno, manteniamo la posizione più alta */
#player2-area.current-turn {
    margin-top: -35px; /* Aumentato leggermente quando è il turno attivo */
}

#player1-area.current-turn::after, #player2-area.current-turn::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, rgba(50, 220, 100, 0.3), rgba(30, 150, 70, 0.3));
    border-radius: 15px;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.8;
    animation: currentTurnGlow 2s ease-in-out infinite alternate;
}

@keyframes currentTurnGlow {
    0% { opacity: 0.5; filter: blur(10px); }
    100% { opacity: 0.8; filter: blur(15px); }
}

/* Mosse valide e non valide */
.cell.valid-move {
    background-color: rgba(30, 150, 70, 0.7);
    animation: pulseValidMove 2s infinite, validMoveGlow 2s infinite alternate;
    border: 3px solid rgba(50, 220, 100, 0.8);
    transform: translateZ(10px) scale(1.05);
    z-index: 10;
}

@keyframes pulseValidMove {
    0% { box-shadow: 0 0 15px rgba(50, 220, 100, 0.6); }
    50% { box-shadow: 0 0 30px rgba(50, 220, 100, 0.9), 0 0 50px rgba(50, 220, 100, 0.4); }
    100% { box-shadow: 0 0 15px rgba(50, 220, 100, 0.6); }
}

@keyframes validMoveGlow {
    0% { box-shadow: inset 0 0 15px rgba(50, 220, 100, 0.5); filter: brightness(1); }
    100% { box-shadow: inset 0 0 30px rgba(50, 220, 100, 0.8); filter: brightness(1.2); }
}

.cell.valid-move::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(50, 220, 100, 0.5), transparent 70%);
    border-radius: 6px;
    z-index: -1;
    filter: blur(8px);
    animation: validMoveRadialGlow 2s infinite alternate;
}

@keyframes validMoveRadialGlow {
    0% { opacity: 0.5; transform: scale(0.9); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

.cell.invalid-move {
    background-color: rgba(150, 30, 30, 0.7);
    animation: pulseInvalidMove 2s infinite, invalidMoveGlow 2s infinite alternate;
    border: 3px solid rgba(255, 60, 60, 0.8);
    transform: translateZ(10px) scale(1.05);
    z-index: 10;
}

@keyframes pulseInvalidMove {
    0% { box-shadow: 0 0 15px rgba(255, 60, 60, 0.6); }
    50% { box-shadow: 0 0 30px rgba(255, 60, 60, 0.9), 0 0 50px rgba(255, 60, 60, 0.4); }
    100% { box-shadow: 0 0 15px rgba(255, 60, 60, 0.6); }
}

@keyframes invalidMoveGlow {
    0% { box-shadow: inset 0 0 15px rgba(255, 60, 60, 0.5); filter: brightness(1); }
    100% { box-shadow: inset 0 0 30px rgba(255, 60, 60, 0.8); filter: brightness(1.2); }
}

.cell.invalid-move::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(255, 60, 60, 0.5), transparent 70%);
    border-radius: 6px;
    z-index: -1;
    filter: blur(8px);
    animation: invalidMoveRadialGlow 2s infinite alternate;
}

@keyframes invalidMoveRadialGlow {
    0% { opacity: 0.5; transform: scale(0.9); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

/* Animazione delle carte */
.hand-area .card-slot .card.selected {
    transform: scale(1.2);
    box-shadow: 0 25px 50px rgba(0,0,0,0.5), 0 0 60px rgba(50, 220, 100, 0.6); /* Bagliore esistente */
    border: 3px solid rgba(50, 220, 100, 0.8); /* Bordo colorato esistente */
    z-index: 100;
    position: relative;
}

/* Stile per l'ultima carta (giocabile solo su vertice) */
.card.last-card {
    opacity: 1;
    cursor: grab !important;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.7) !important; /* Oro */
    border: 2px solid rgba(255, 215, 0, 0.8) !important;
    animation: lastCardPulse 2s infinite alternate;
}

@keyframes lastCardPulse {
    0% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.7) !important; }
    100% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.9) !important; }
}

/* Stile per i vertici neutrali quando un giocatore ha solo una carta - regola spostata più in basso per evitare duplicati */

/* Regola rimossa - ora gestita nella regola principale sopra */

/* BUGFIX: I vertici conquistati quando sono playable mantengono il loro aspetto normale con solo un bordo dorato */
.vertex[data-controller="white"].playable-vertex {
    border: 3px solid rgba(255, 215, 0, 0.8) !important; /* Solo bordo dorato, mantiene tutto il resto */
}

.vertex[data-controller="black"].playable-vertex {
    border: 3px solid rgba(255, 215, 0, 0.8) !important; /* Solo bordo dorato, mantiene tutto il resto */
}

/* Vertici neutrali per il tabellone vuoto - stile più leggero che non interferisce con i vertici normali */
.vertex.neutral-vertex {
    background: rgba(240, 240, 240, 0.5) !important;
    box-shadow: 0 0 10px rgba(200, 200, 200, 0.3) !important;
    border: 2px solid rgba(180, 180, 180, 0.4) !important;
    animation: none !important;
    transform: none !important;
}

@keyframes vertexPulse {
    0% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.7), inset 0 0 10px rgba(255, 215, 0, 0.5); }
    100% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.9), inset 0 0 20px rgba(255, 215, 0, 0.7); }
}

/* BUGFIX: Nessun effetto radiale per vertici conquistati - solo il bordo dorato è sufficiente */

/* Per i vertici neutrali playable, usiamo box-shadow aggiuntivo invece di pseudo-elementi */
.vertex[data-controller="none"].playable-vertex {
    background: linear-gradient(to right, #ffffff 50%, #000000 50%) !important; /* Mantiene aspetto neutrale */
    background-color: transparent !important; /* Previene override del background-color */
    border: 3px solid rgba(255, 215, 0, 0.8) !important; /* Bordo dorato */
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.7), inset 0 0 15px rgba(255, 215, 0, 0.5), 0 0 15px rgba(150,150,150,0.7) !important;
    animation: vertexPulseNeutral 2s infinite alternate !important;
    z-index: 10;
    position: relative;
    transform: scale(1.05);
}

@keyframes vertexRadialGlow {
    0% { opacity: 0.5; transform: scale(0.9); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

/* Evidenziazione per l'ultima carta */
.last-card-highlight {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), transparent 70%);
    border-radius: 5px;
    z-index: 10;
    pointer-events: none;
    animation: highlightPulse 2s infinite alternate;
}

@keyframes highlightPulse {
    0% { opacity: 0.5; }
    100% { opacity: 0.8; }
}

/* Manteniamo la classe card-overlay per compatibilità */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 5px;
}

/* Testo nell'overlay */
.overlay-text {
    color: white;
    font-weight: bold;
    font-size: 1.2em;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
    padding: 5px 10px;
    border-radius: 5px;
    transform: rotate(-15deg);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Regole specifiche per garantire che le carte siano sempre visibili e disposte in 2 righe da 5 a qualsiasi zoom */
@media screen and (min-width: 1px) {
    /* Regole base per tutte le dimensioni */
    .hand-area {
        display: grid !important;
        grid-template-columns: repeat(5, minmax(90px, 1fr)) !important;
        grid-template-rows: repeat(2, auto) !important;
        gap: 4px !important;
        padding: 6px !important;
        min-height: 0 !important; /* Rimuove l'altezza minima fissa */
        max-height: 100% !important; /* Assicura che non superi mai il contenitore */
        overflow: hidden !important;
        box-sizing: border-box !important;
        border: 1px solid rgba(100, 150, 255, 0.2) !important;
    }

    /* Zoom 100% - Dimensioni ottimali per evitare tagli */
    .card-slot, .card {
        width: 90px !important;
        height: 131px !important; /* Mantiene il rapporto 1.46:1 */
        margin: 0px !important;
        border-radius: 5px !important;
        box-sizing: border-box !important;
        border: 1px solid rgba(100, 150, 255, 0.2) !important;
        transform: scale(1) !important;
    }

    .card-value {
        font-size: 24px !important;
    }

    .card-suit {
        font-size: 34px !important;
    }

    /* Adattamenti per schermi 2K e superiori (1440p e oltre) */
    @media screen and (min-width: 2000px) {
        .card-slot, .card {
            width: 120px !important;
            height: 175px !important; /* Mantiene il rapporto 1.46:1 */
            margin: 0px !important;
            border-radius: 8px !important;
            background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9)) !important;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3) !important;
            border: 1px solid rgba(100, 150, 255, 0.3) !important;
        }

        .card-slot:hover {
            background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9)) !important;
            border: 1px solid rgba(120, 180, 255, 0.4) !important;
        }

        .hand-area {
            gap: 6px !important;
            padding: 8px !important;
        }

        .card-value {
            font-size: 30px !important;
        }

        .card-suit {
            font-size: 40px !important;
        }
    }

    /* Adattamenti per schermi tra full HD e 2K */
    @media screen and (min-width: 1800px) and (max-width: 1999px) {
        .card-slot, .card {
            width: 110px !important;
            height: 160px !important; /* Mantiene il rapporto 1.46:1 */
            margin: 0px !important;
            border-radius: 7px !important;
            background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9)) !important;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3) !important;
            border: 1px solid rgba(100, 150, 255, 0.3) !important;
        }

        .card-slot:hover {
            background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9)) !important;
            border: 1px solid rgba(120, 180, 255, 0.4) !important;
        }

        .hand-area {
            gap: 5px !important;
            padding: 7px !important;
        }

        .card-value {
            font-size: 28px !important;
        }

        .card-suit {
            font-size: 38px !important;
        }
    }

    /* Adattamenti per schermi medi */
    @media screen and (max-width: 1799px) and (min-width: 1201px) {
        .card-slot, .card {
            width: 100px !important;
            height: 146px !important; /* Mantiene il rapporto 1.46:1 */
            margin: 0px !important;
            border-radius: 6px !important;
            background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9)) !important;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3) !important;
            border: 1px solid rgba(100, 150, 255, 0.3) !important;
        }

        .card-slot:hover {
            background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9)) !important;
            border: 1px solid rgba(120, 180, 255, 0.4) !important;
        }

        .hand-area {
            gap: 4px !important;
            padding: 6px !important;
        }

        .card-value {
            font-size: 26px !important;
        }

        .card-suit {
            font-size: 36px !important;
        }
    }

    /* Adattamenti per schermi più piccoli */
    @media screen and (max-width: 1200px) and (min-width: 993px) {
        .card-slot, .card {
            width: 90px !important;
            height: 131px !important; /* Mantiene il rapporto 1.46:1 */
            margin: 0px !important;
            border-radius: 5px !important;
            background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9)) !important;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3) !important;
            border: 1px solid rgba(100, 150, 255, 0.3) !important;
        }

        .card-slot:hover {
            background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9)) !important;
            border: 1px solid rgba(120, 180, 255, 0.4) !important;
        }

        .hand-area {
            gap: 3px !important;
            padding: 5px !important;
        }

        .card-value {
            font-size: 24px !important;
        }

        .card-suit {
            font-size: 34px !important;
        }
    }
}

.hand-area .card-slot .card.selected::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -25px;
    bottom: -25px;
    background: radial-gradient(circle at center, rgba(50, 220, 100, 0.5), transparent 70%);
    border-radius: 15px;
    z-index: -1;
    filter: blur(10px);
    animation: selectedCardGlow 1.5s infinite alternate;
}

@keyframes selectedCardGlow {
    0% { opacity: 0.5; transform: scale(0.9); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

#dice-result-text {
    font-size: 1.4em;
    font-weight: 700;
    margin-top: 20px;
    color: #e0f0ff;
    text-shadow: 0 0 12px rgba(100, 180, 255, 0.8);
    text-align: center;
    padding: 15px 25px;
    background: rgba(20, 40, 80, 0.6);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(0, 100, 255, 0.2);
    border: 2px solid rgba(100, 150, 255, 0.3);
    transition: all 0.3s ease;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

#dice-result-text ul {
    margin: 0;
    padding: 0;
    list-style-position: inside;
}

#dice-result-text li {
    margin-bottom: 8px;
    text-align: left;
    line-height: 1.4;
}

#dice-result-text li:last-child {
    margin-bottom: 0;
}

#dice-result-text:hover {
    background: rgba(20, 40, 80, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 100, 255, 0.3);
}

/* Stile per il riquadro di vittoria */
#victory-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.victory-container {
    background: linear-gradient(135deg, #2c3e50 0%, #1a2a3a 100%);
    border-radius: 20px;
    padding: 40px;
    width: 90%;
    max-width: 550px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 30px rgba(255, 215, 0, 0.5);
    text-align: center;
    color: white;
    border: 4px solid rgba(255, 215, 0, 0.7);
    animation: victoryPulse 2s infinite alternate, slideIn 0.5s ease-out;
    transform-style: preserve-3d;
    perspective: 1000px;
}

@keyframes victoryPulse {
    0% { box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 215, 0, 0.5); }
    100% { box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 50px rgba(255, 215, 0, 0.9); }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.victory-header {
    margin-bottom: 20px;
    position: relative;
}

.victory-icon {
    font-size: 5em;
    color: gold;
    margin-bottom: 20px;
    display: block;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.9);
    animation: trophyGlow 1.5s infinite alternate;
}

@keyframes trophyGlow {
    0% { transform: scale(1); text-shadow: 0 0 30px rgba(255, 215, 0, 0.8); }
    100% { transform: scale(1.15); text-shadow: 0 0 60px rgba(255, 215, 0, 1); }
}

.victory-header h2 {
    font-size: 3em;
    margin: 0;
    background: linear-gradient(to right, #f1c40f, #f39c12);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

.victory-content {
    margin: 30px 0;
    font-size: 1.2em;
}

#rating-changes {
    margin: 15px 0;
    text-align: center;
}

.rating-change {
    font-size: 1.1rem;
    margin: 5px 0;
    transition: all 0.3s ease;
}

.winner-rating {
    color: #4CAF50;
}

.loser-rating {
    color: #f44336;
}

.rating-diff {
    font-weight: bold;
    display: inline-block;
    min-width: 45px;
}

#winner-name {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 15px;
    color: #f1c40f;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

#victory-reason {
    font-style: italic;
    color: #ecf0f1;
    line-height: 1.6;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    margin: 15px 0;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.victory-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.victory-buttons button {
    padding: 12px 20px;
    font-size: 1.1em;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.victory-buttons .primary-btn {
    background: linear-gradient(135deg, #f1c40f, #e67e22);
    border: none;
    color: #fff;
    box-shadow: 0 4px 15px rgba(241, 196, 15, 0.4);
}

.victory-buttons .primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(241, 196, 15, 0.6);
}

.victory-buttons .secondary-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: #fff;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.victory-buttons .secondary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(52, 152, 219, 0.6);
}

/* Setup animazione */
#setup-animation {
    text-align: center;
    background: linear-gradient(135deg, rgba(15, 25, 40, 0.9), rgba(10, 15, 25, 0.9));
    padding: 70px;
    border-radius: 30px;
    box-shadow: 0 30px 60px rgba(0,0,0,0.5), 0 0 100px rgba(0, 100, 255, 0.3), inset 0 0 40px rgba(0, 30, 60, 0.5);
    width: 98%;
    max-width: 800px;
    margin: 0 auto;
    border: 2px solid rgba(50, 100, 200, 0.4);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    z-index: 100;
}

#setup-animation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.2;
    pointer-events: none;
    z-index: -1;
}

#setup-animation h2 {
    color: #e0f0ff;
    margin-bottom: 30px;
    font-size: 2em;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.7);
    letter-spacing: 2px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

#setup-animation h2::after {
    content: '';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, rgba(100, 150, 255, 0.8), transparent);
    box-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

#dice-area {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 320px;
    gap: 110px;
    margin: 60px auto;
    perspective: 1500px;
    position: relative;
    max-width: 750px;
    background: radial-gradient(circle at center, rgba(30, 60, 120, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    box-shadow: inset 0 0 100px rgba(0, 100, 255, 0.2);
    animation: diceAreaGlow 4s infinite alternate;
}

@keyframes diceAreaGlow {
    0% { box-shadow: inset 0 0 100px rgba(0, 100, 255, 0.1); }
    100% { box-shadow: inset 0 0 150px rgba(0, 100, 255, 0.3); }
}

#dice-area .dice {
    width: 65px;
    height: 65px;
    position: relative;
    transform-style: preserve-3d;
    margin: 0;
    transform: scale(0.8);
    cursor: pointer;
    animation: dice-roll 2s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
    animation-play-state: paused;
    filter: drop-shadow(0 0 20px rgba(0, 100, 255, 0.4));
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Regola specifica per i dadi durante l'animazione */
#dice-area .dice.animating .g1,
#dice-area .dice.animating .g2 {
    transform: scale(1.5) !important; /* Aumento significativo durante l'animazione */
    transition: transform 0.3s ease-out;
}

#dice-area .dice:hover {
    transform: scale(0.9) rotateX(15deg) rotateY(15deg) translateZ(10px);
    filter: drop-shadow(0 0 30px rgba(0, 150, 255, 0.6)) brightness(1.2);
}

/* Facce del dado 3D */
#dice-area .dice .face {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 8px; /* Ridotto da 18px per evitare sovrapposizioni */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2em;
    font-weight: bold;
    transition: all 0.3s;
    border: 2px solid rgba(255,255,255,0.3);
    transform: translateZ(0);
    box-shadow: inset 0 0 15px rgba(0,0,0,0.3);
    background-image: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
    backdrop-filter: blur(2px);
    color: #ffffff;
    text-shadow: 0 0 5px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5), 0 0 2px rgba(0,0,0,1);
    backface-visibility: hidden; /* Nasconde le facce posteriori */
}

/* Effetto brillante sulle facce dei dadi */
#dice-area .dice .face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 60%);
    border-radius: 8px; /* Ridotto per coerenza con la faccia */
    pointer-events: none;
    z-index: 1;
}

/* Posizionamento delle facce del dado */
#dice-area .dice .face.front { transform: translateZ(32.5px); }
#dice-area .dice .face.back { transform: rotateY(180deg) translateZ(32.5px); }
#dice-area .dice .face.right { transform: rotateY(90deg) translateZ(32.5px); }
#dice-area .dice .face.left { transform: rotateY(-90deg) translateZ(32.5px); }
#dice-area .dice .face.top { transform: rotateX(90deg) translateZ(32.5px); }
#dice-area .dice .face.bottom { transform: rotateX(-90deg) translateZ(32.5px); }

/* Effetto particellare attorno ai dadi */
#dice-area .dice::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 10px; /* Ridotto per coerenza con la faccia */
    background: radial-gradient(circle at 50% 50%,
        rgba(255,255,255,0.8) 0%,
        rgba(255,255,255,0) 70%);
    filter: blur(10px);
    opacity: 0;
    z-index: -1;
    transform: scale(1.2);
    animation: dice-glow 3s ease-out infinite;
}

/* Facce del dado di colore con after modificato */
#dice-area .dice.alpha .face::after,
#dice-area .dice.color .face::after,
#dice-area .dice.numeric .face::after {
    border-radius: 8px; /* Ridotto per coerenza */
}

/* Anche gli after/before per i vari tipi di dado devono avere bordi arrotondati coerenti */
#dice-area .dice.alpha .face::after,
#dice-area .dice.numeric .face::after,
#dice-area .dice.color.white .face::after,
#dice-area .dice.color.black .face::after {
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 15px; /* Ridotto per coerenza */
}

/* Aggiorniamo anche i cubi nella scena 3D */
.cube__face {
    position: absolute;
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 80px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
    border-radius: 20px; /* Aumentato per angoli più smussati */
    backface-visibility: visible;
    overflow: hidden; /* Aggiunto per evitare clipping del contenuto */
}

@keyframes dice-glow {
    0% { opacity: 0; transform: scale(1.2); }
    20% { opacity: 0.7; transform: scale(1.5); }
    60% { opacity: 0.3; transform: scale(1.8); }
    100% { opacity: 0; transform: scale(2); }
}

@keyframes shadow-movement {
    0% { opacity: 0; transform: scaleX(0.8); }
    20% { opacity: 0.2; transform: scaleX(1.5) translateY(-40px); }
    40% { opacity: 0.4; transform: scaleX(1.2) translateY(-20px); }
    60% { opacity: 0.6; transform: scaleX(1.4) translateY(-10px); }
    80% { opacity: 0.8; transform: scaleX(1.3) translateY(-5px); }
    100% { opacity: 0.5; transform: scaleX(1.3) translateY(0); }
}

@keyframes result-appear {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Contenitore per mazzo */

.deck-container {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 10px 0;
    width: 100%;
    text-align: center;
    flex: 1;
    max-width: 100%;
    box-sizing: border-box;
}

#deck {
    position: relative;
    width: 90px;
    height: 126px;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 5px auto 0 auto;
    max-width: 100%;
    transform-style: preserve-3d;
    perspective: 1000px;
    transform: rotateX(5deg);
    filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.5));
    animation: deckFloat 4s infinite alternate ease-in-out;
}

@keyframes deckFloat {
    0% {
        transform: translateY(0px) rotateX(5deg) scale(1);
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.5));
    }
    50% {
        transform: translateY(-6px) rotateX(8deg) scale(1.02);
        filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.6));
    }
    100% {
        transform: translateY(-2px) rotateX(6deg) scale(1.01);
        filter: drop-shadow(0 12px 25px rgba(0, 0, 0, 0.55));
    }
}

.deck-counter-area {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(30, 48, 70, 0.9), rgba(40, 60, 85, 0.95));
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.5),
        0 0 25px rgba(0, 120, 255, 0.4),
        inset 0 0 15px rgba(0, 100, 255, 0.2);
    margin: 0;
    border: 2.5px solid rgba(130, 190, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    perspective: 500px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    animation: counterPulse 4s infinite alternate ease-in-out;
    z-index: 20;
    pointer-events: none; /* Importante: impedisce al contatore di catturare gli eventi del mouse */
}

@keyframes counterPulse {
    0% {
        box-shadow:
            0 8px 20px rgba(0, 0, 0, 0.4),
            0 0 25px rgba(0, 120, 255, 0.3),
            inset 0 0 12px rgba(0, 100, 255, 0.15);
        border-color: rgba(120, 180, 255, 0.4);
    }
    100% {
        box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.5),
            0 0 35px rgba(0, 150, 255, 0.4),
            inset 0 0 15px rgba(0, 120, 255, 0.25);
        border-color: rgba(140, 200, 255, 0.5);
    }
}

.deck-counter-area:hover {
    box-shadow: 0 0 12px 4px rgba(135, 206, 250, 0.6); /* Esempio: bagliore azzurro chiaro */
    z-index: 10;
    border-color: rgba(100, 150, 255, 0.6);
    /* Aggiunta una transizione più specifica per il box-shadow se si vuole un effetto smooth */
    /* La transizione originale 'all 0.3s ...' è stata rimossa dalla definizione di .cell, quindi la mettiamo qui specifica */
    transition: background-color 0.3s cubic-bezier(0.19, 1, 0.22, 1),
                border-color 0.3s cubic-bezier(0.19, 1, 0.22, 1),
                box-shadow 0.3s ease-out;
}

.deck-label {
    color: #e8f8ff;
    font-size: 1.2em;
    font-weight: 600;
    text-shadow:
        0 0 10px rgba(100, 180, 255, 0.7),
        0 0 20px rgba(50, 150, 255, 0.4);
    letter-spacing: 0.5px;
    transform: translateZ(5px);
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
}

.deck-counter-area:hover .deck-label {
    text-shadow:
        0 0 15px rgba(120, 200, 255, 0.8),
        0 0 30px rgba(60, 160, 255, 0.5);
    letter-spacing: 1px;
}

.card-count {
    color: #ff6b57;
    font-weight: bold;
    font-size: 1.5em;
    text-shadow:
        0 0 10px rgba(231, 76, 60, 0.8),
        0 0 20px rgba(200, 50, 30, 0.5);
    transform: translateZ(10px);
    transition: all 0.3s ease;
    position: relative;
    animation: numberPulse 2s infinite alternate ease-in-out;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: -0.5px;
    pointer-events: none; /* Assicura che anche il testo non catturi gli eventi mouse */
}

@keyframes numberPulse {
    0% {
        text-shadow:
            0 0 10px rgba(231, 76, 60, 0.7),
            0 0 20px rgba(200, 50, 30, 0.4);
        transform: translateZ(10px) scale(1);
    }
    100% {
        text-shadow:
            0 0 15px rgba(255, 90, 70, 0.8),
            0 0 30px rgba(220, 60, 40, 0.5);
        transform: translateZ(15px) scale(1.1);
    }
}

.deck-counter-area:hover .card-count {
    text-shadow:
        0 0 20px rgba(255, 100, 80, 0.9),
        0 0 40px rgba(230, 70, 50, 0.6);
    animation-duration: 1s;
}

/* Animazione rimossa */

#deck[data-cards="few"] + .deck-counter-area .card-count {
    color: #ffaa50; /* Arancione per "poche carte" */
    text-shadow:
        0 0 10px rgba(255, 170, 80, 0.8),
        0 0 20px rgba(220, 150, 30, 0.5);
    animation: fewCardsPulse 1.5s infinite alternate ease-in-out;
}

@keyframes fewCardsPulse {
    0% {
        text-shadow:
            0 0 10px rgba(255, 170, 80, 0.7),
            0 0 20px rgba(220, 150, 30, 0.4);
        transform: translateZ(10px) scale(1);
    }
    100% {
        text-shadow:
            0 0 15px rgba(255, 180, 90, 0.8),
            0 0 30px rgba(235, 160, 40, 0.5);
        transform: translateZ(15px) scale(1.15);
    }
}

#deck[data-cards="empty"] + .deck-counter-area .card-count {
    color: #ff5050; /* Rosso per "mazzo vuoto" */
    text-shadow:
        0 0 10px rgba(255, 80, 80, 0.8),
        0 0 20px rgba(220, 50, 50, 0.5);
    animation: emptyDeckPulse 1s infinite alternate ease-in-out;
}

@keyframes emptyDeckPulse {
    0% {
        text-shadow:
            0 0 10px rgba(255, 80, 80, 0.7),
            0 0 20px rgba(220, 50, 50, 0.4);
        transform: translateZ(10px) scale(1);
    }
    100% {
        text-shadow:
            0 0 20px rgba(255, 100, 100, 0.9),
            0 0 40px rgba(235, 60, 60, 0.6);
        transform: translateZ(20px) scale(1.2);
    }
}

/* Rimuoviamo l'animazione pulse dal contatore */
/* @keyframes pulseCardCount { ... } */

/* Rimuoviamo l'hover specifico del contatore */
/* #deck:hover .card-count { ... } */

.card-stack-visual {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
    max-width: 100%;
    opacity: 1 !important; /* Sempre visibile anche quando il deck-discard-area è invisibile */
    z-index: 10; /* Assicura che sia sopra altri elementi */
}

.card-pile-1,
.card-pile-2,
.card-pile-3,
.card-pile-4,
.card-pile-5 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    transform-origin: center center;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.card-pile-1 { transform: translateZ(1px); }
.card-pile-2 { transform: translateZ(2px); opacity: 0.95; }
.card-pile-3 { transform: translateZ(3px); opacity: 0.9; }
.card-pile-4 { transform: translateZ(4px); opacity: 0.85; }
.card-pile-5 { transform: translateZ(5px); opacity: 0.8; }

/* Rimossi gli effetti hover sul mazzo */

/* Animazione della carta superiore che scorre verso l'alto */
@keyframes drawCardHint {
    0% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    }
    40% {
        transform: translateY(-20px) rotateX(-8deg);
        box-shadow: 0 25px 35px rgba(0,0,0,0.5), 0 0 40px rgba(0, 150, 255, 0.6);
        filter: brightness(1.15);
    }
    45% {
        transform: translateY(-20px) rotateX(-8deg);
        box-shadow: 0 25px 35px rgba(0,0,0,0.5), 0 0 40px rgba(0, 150, 255, 0.6);
        filter: brightness(1.15);
    }
    85% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
        filter: brightness(1);
    }
    100% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    }
}

/* Carta principale */
.card-stack-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background: url('img/carte/card-back.webp');
    background-size: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    /* Rimosse transizioni e animazioni */
    overflow: hidden;
    z-index: 10;
    cursor: pointer; /* Indica che il mazzo è cliccabile */
}

/* Aggiungi un indicatore visivo al mazzo quando viene cliccato */
#deck:active .card-stack-visual::before {
    transform: translateY(-10px) scale(0.97);
    filter: brightness(0.9);
    animation-play-state: paused;
}

.card-stack-visual::before::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%);
    border-radius: 10px;
    pointer-events: none;
}

/* Carta sottostante */
.card-stack-visual::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--stack-height);
    border-radius: 12px;
    background: linear-gradient(135deg, #1a5a8a, #2271b3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transform: translateZ(-5px) translateX(-6px) translateY(-6px);
    opacity: 0.9;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3), 0 0 20px rgba(0, 100, 255, 0.2);
}

/* Variazioni di altezza per il mazzo in base al numero di carte */
#deck[data-cards="few"] .card-pile-4,
#deck[data-cards="few"] .card-pile-5 {
    display: none;
}

#deck[data-cards="medium"] .card-pile-5 {
    display: none;
}

/* Effetti hover più pronunciati per il mazzo */
#deck:hover .card-stack-visual::before {
    transform: translateZ(-2px) translateX(-3px) translateY(-5px) rotateZ(3deg);
    box-shadow: 0 15px 35px rgba(0,0,0,0.4), 0 0 50px rgba(0, 100, 255, 0.4);
    animation-play-state: paused;
}

#deck:hover .card-pile-1 {
    transform: translateZ(-3px) translateX(-6px) translateY(-6px) rotateZ(2deg);
}

#deck:hover .card-pile-2 {
    transform: translateZ(-6px) translateX(-10px) translateY(-10px) rotateZ(4deg);
}

#deck:hover .card-pile-4 {
    transform: translateZ(-12px) translateX(-18px) translateY(-18px) rotateZ(8deg);
}

#deck:hover .card-pile-5 {
    transform: translateZ(-15px) translateX(-22px) translateY(-22px) rotateZ(10deg);
}

#deck:hover .card-count {
    color: #e0f0ff;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.9);
}

/* Notazioni del tabellone - migliorata la visibilità e il posizionamento */
.board-notation {
    position: absolute;
    color: #e0f0ff;
    font-size: 16px;
    font-weight: bold;
    z-index: 5;
    text-shadow: 0 0 12px rgba(100, 150, 255, 0.8);
    text-align: center;
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(20, 40, 80, 0.8);
    border-radius: 50%;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.25);
    transform: translate(-50%, -50%);
    border: 2px solid rgba(100, 150, 255, 0.5);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(2px);
}

.board-notation:hover {
    transform: translate(-50%, -50%) scale(1.3);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 100, 255, 0.4);
    background-color: rgba(30, 60, 120, 0.9);
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.9);
    border-color: rgba(100, 150, 255, 0.6);
}

/* Notazioni superiori (Lettere) */
.notation-top {
    top: -15px; /* Allontanato dalle celle */
}

/* Notazioni inferiori (Lettere) */
.notation-bottom {
    bottom: -25px; /* Allontanato dalle celle */
}

/* Notazioni laterali (Numeri) */
.notation-left {
    left: -15px; /* Allontanato dalle celle */
}

/* Notazioni laterali (Numeri) */
.notation-right {
    right: -25px; /* Allontanato dalle celle */
}

/* Posizionamento equidistante delle notazioni */
/* Spazio totale celle orizzontale: 6 * var(--cell-width) + 5 * 4px = 6 * var(--cell-width) + 20px */
/* Spazio totale celle verticale: 6 * var(--cell-height) + 5 * 4px = 6 * var(--cell-height) + 20px */
/* Padding: 50px */
/* Gap tra celle: 4px (usato nei calcoli sotto) */

/* Notazioni Orizzontali (Lettere A-F) - Posizionate equidistantemente */
.notation-a { left: calc(50px + (0.5 * (6 * var(--cell-width) + 20px) / 6)); }
.notation-b { left: calc(50px + (1.5 * (6 * var(--cell-width) + 20px) / 6)); }
.notation-c { left: calc(50px + (2.5 * (6 * var(--cell-width) + 20px) / 6)); }
.notation-d { left: calc(50px + (3.5 * (6 * var(--cell-width) + 20px) / 6)); }
.notation-e { left: calc(50px + (4.5 * (6 * var(--cell-width) + 20px) / 6)); }
.notation-f { left: calc(50px + (5.5 * (6 * var(--cell-width) + 20px) / 6)); }

/* Notazioni Verticali (Numeri 1-6) - Posizionate equidistantemente */
.notation-1 { top: calc(50px + (0.5 * (6 * var(--cell-height) + 20px) / 6)); }
.notation-2 { top: calc(50px + (1.5 * (6 * var(--cell-height) + 20px) / 6)); }
.notation-3 { top: calc(50px + (2.5 * (6 * var(--cell-height) + 20px) / 6)); }
.notation-4 { top: calc(50px + (3.5 * (6 * var(--cell-height) + 20px) / 6)); }
.notation-5 { top: calc(50px + (4.5 * (6 * var(--cell-height) + 20px) / 6)); }
.notation-6 { top: calc(50px + (5.5 * (6 * var(--cell-height) + 20px) / 6)); }

/* Responsività per schermi più piccoli */
@media (max-width: 1200px) {
    #game-container {
        grid-template-areas:
            "sidebar players board"
            "sidebar log log";
        grid-template-columns: min-content 250px 1fr;
        grid-template-rows: 1fr auto;
        height: 100vh;
        max-height: 100vh;
        padding: 0;
        padding-right: 0.6vw;
        padding-bottom: 0.6vw;
    }

    #board-area {
        grid-template-areas:
            "game-board board-sidebar";
        height: calc(100% - 25px); /* Ridotta l'altezza per non toccare il bordo superiore */
        margin-top: 10px; /* Aggiunto margine superiore per creare spazio */
    }

    #log-area {
        height: 150px;
        margin-top: -15px;
    }

    .game-sidebar {
        width: 50px; /* Ancora più compresso su schermi più piccoli */
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    /* Rimosso l'espansione al passaggio del mouse per schermi più piccoli */
}

/* Small mobile devices (800px and below) */
@media (max-width: 800px) {
    #game-container {
        grid-template-areas:
            "sidebar board"
            "sidebar players";
        grid-template-columns: var(--sidebar-width-collapsed) 1fr;
        grid-template-rows: 60vh 40vh;
        height: 100vh;
        max-height: 100vh;
        padding: var(--space-xxs);
        gap: var(--space-xs);
    }

    #board-area {
        grid-template-areas:
            "game-board"
            "board-sidebar";
        grid-template-columns: 1fr;
        grid-template-rows: 75% 25%;
        height: 100%;
        margin-top: 0;
        padding: var(--space-xs);
        overflow: hidden;
    }

    /* Fix per evitare sovrapposizioni nei piccoli schermi */
    #game-board {
        transform: scale(0.9);
        margin: 0 auto;
    }

    #players-column {
        flex-direction: row;
        height: auto;
        gap: var(--space-sm);
        padding: var(--space-xs);
    }

    #player1-area, #player2-area {
        width: calc(50% - var(--space-xs));
        max-height: 100%;
    }

    /* Ridimensionamento carte per evitare sovrapposizioni */
    .hand-area {
        grid-template-columns: repeat(5, 1fr);
    }

    .game-sidebar {
        width: var(--sidebar-width-collapsed); /* Usa la variabile CSS */
        padding: 0;
        padding-bottom: var(--space-sm);
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .game-sidebar .logo {
        padding: 30px 0 10px 0;
    }

    /* Rimosso l'espansione al passaggio del mouse per schermi molto piccoli */

    .game-sidebar .logo .logo-image {
        width: 35px;
    }

    .game-sidebar li i {
        font-size: 18px;
        width: 40px;
    }

    /* Rimosso lo stile degli elementi li i al passaggio del mouse sulla sidebar per schermi piccoli */
}

/* Logo Skemino sul rombo centrale */
.skemino-logo {
    display: none; /* Nascondo completamente il logo */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    font-weight: bold;
    font-style: italic;
    font-size: 20px;
    color: #e0f0ff;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.9), 0 0 30px rgba(0, 100, 255, 0.6);
    z-index: 10;
    white-space: nowrap;
    text-align: center;
    width: 120px;
    pointer-events: none;
    letter-spacing: 2px;
    animation: logoGlow 3s infinite alternate;
}

@keyframes logoGlow {
    0% { text-shadow: 0 0 15px rgba(100, 150, 255, 0.7), 0 0 30px rgba(0, 100, 255, 0.4); }
    100% { text-shadow: 0 0 20px rgba(100, 150, 255, 0.9), 0 0 40px rgba(0, 100, 255, 0.6); }
}

/* Colori celle d'angolo */
.cell[data-notation="a1"] {
    background: linear-gradient(135deg, rgba(30, 120, 255, 0.9), rgba(20, 80, 200, 0.9));
    border: 2px solid rgba(100, 150, 255, 0.6);
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(30, 120, 255, 0.3);
}

.cell[data-notation="a1"]::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(30, 120, 255, 0.5), transparent 70%);
    border-radius: 10px;
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

.cell[data-notation="f1"] {
    background: linear-gradient(135deg, rgba(50, 220, 100, 0.9), rgba(30, 180, 70, 0.9));
    border: 2px solid rgba(100, 255, 150, 0.6);
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(50, 220, 100, 0.3);
}

.cell[data-notation="f1"]::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(50, 220, 100, 0.5), transparent 70%);
    border-radius: 10px;
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

.cell[data-notation="a6"] {
    background: linear-gradient(135deg, rgba(255, 60, 60, 0.9), rgba(200, 40, 40, 0.9));
    border: 2px solid rgba(255, 100, 100, 0.6);
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 60, 60, 0.3);
}

.cell[data-notation="a6"]::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(255, 60, 60, 0.5), transparent 70%);
    border-radius: 10px;
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

.cell[data-notation="f6"] {
    background: linear-gradient(135deg, rgba(255, 220, 50, 0.9), rgba(220, 180, 30, 0.9));
    border: 2px solid rgba(255, 220, 100, 0.6);
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 220, 50, 0.3);
}

.cell[data-notation="f6"]::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle at center, rgba(255, 220, 50, 0.5), transparent 70%);
    border-radius: 10px;
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
}

/* Stile per la carta che viene piazzata inizialmente */
.initial-card {
    z-index: 20;
    animation: initialCardPulse 2s infinite alternate;
}

@keyframes initialCardPulse {
    0% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.4); transform: scale(1); }
    100% { box-shadow: 0 0 40px rgba(255, 255, 255, 0.7), 0 0 60px rgba(100, 150, 255, 0.5); transform: scale(1.05); }
}

/* Classe per le carte durante l'animazione iniziale */
.initial-card-animation {
    position: absolute;
    z-index: 90;
    pointer-events: none;
    animation: initialCardFly 1.5s cubic-bezier(0.2, 0.8, 0.2, 1.2);
}

@keyframes initialCardFly {
    0% { opacity: 0; transform: translateY(-300px) scale(0.5) rotate(-20deg); }
    60% { opacity: 1; transform: translateY(30px) scale(1.2) rotate(10deg); }
    80% { transform: translateY(-10px) scale(0.95) rotate(-5deg); }
    100% { transform: translateY(0) scale(1) rotate(0); }
}

/* Animazione inizio battaglia */
#battle-start-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    backdrop-filter: blur(5px);
    background: radial-gradient(circle at center, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.6) 100%);
    transition: opacity 0.3s ease-in-out;
}

#battle-start-overlay.active {
    opacity: 1;
    animation: battleOverlayFade 2.5s forwards;
}

@keyframes battleOverlayFade {
    0% { opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

.battle-start-text {
    font-size: 5em;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 20px rgba(255, 100, 50, 0.8), 0 0 40px rgba(255, 50, 0, 0.6);
    transform: scale(0.5);
    opacity: 0;
    text-align: center;
    letter-spacing: 3px;
    background: linear-gradient(to bottom, #fff, #ffd700);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    white-space: nowrap;
    padding: 0 20px;
}

/* Stile per la parola VS nell'animazione di battaglia */
.battle-start-text::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, transparent, rgba(255, 215, 0, 0.7), transparent);
    bottom: -10px;
    left: 0;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.battle-start-text.active {
    animation: battleTextAppear 2s forwards;
}

@keyframes battleTextAppear {
    0% { transform: scale(0.5); opacity: 0; }
    30% { transform: scale(1.2); opacity: 1; }
    50% { transform: scale(1); opacity: 1; }
    80% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(20); opacity: 0; }
}

.battle-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 999;
}

.battle-particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.9), rgba(255, 100, 0, 0));
    border-radius: 50%;
    pointer-events: none;
    animation: particleFade 2s forwards;
}

@keyframes particleFade {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1); opacity: 1; }
    100% { transform: scale(0); opacity: 0; }
}

/* Stili per l'animazione di distribuzione delle carte */
.dealing-card {
    background-image: url('img/carte/card-back.webp') !important;
    background-color: #1a3a6a !important; /* Colore di fallback */
    background-size: 100% 100% !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

/* Fallback per browser che non supportano WebP */
@supports not (background-image: url('img/carte/card-back.webp')) {
    .dealing-card {
        background-image: url('img/carte/card-back.webp') !important;
    }
}

.dealing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 60%);
    border-radius: 8px;
    z-index: 1;
    pointer-events: none;
}

/* Stili per la scia delle carte durante la distribuzione */
.card-trail {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    z-index: 99;
    pointer-events: none;
    filter: blur(2px);
}

/* Animazione pulsante per mazzo quando è il proprio turno */
#deck[data-cards]:not([disabled]):hover .card-stack-visual {
    /* Rimossa l'animazione di pulse */
    transform: translateY(0) rotateZ(0deg);
    filter: brightness(1);
}

/* Keyframes rimossi */

#deck[data-cards]:not([disabled])::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -25px;
    bottom: -25px;
    background: radial-gradient(circle at center, rgba(0, 100, 255, 0.5), transparent 70%);
    border-radius: 15px;
    z-index: -1;
    filter: blur(10px);
    opacity: 0;
    animation: deckGlow 2s infinite alternate;
}

@keyframes deckGlow {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 0.7; transform: scale(1.1); }
}

#deck .card-count {
    opacity: 0.9;
    transform: translateY(0);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: #e0f0ff;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.7);
    font-size: 3.0em; /* Assicura che la dimensione sia consistente */
}

#deck:hover .card-count {
    opacity: 1;
    transform: translateY(5px);
    color: #e0f0ff;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.9);
}

#discard-pile {
    width: 85px;
    height: 110px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    background: linear-gradient(135deg, rgba(30, 40, 50, 0.9), rgba(20, 25, 35, 0.9));
    box-shadow: 0 8px 20px rgba(0,0,0,0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    color: #e0f0ff;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.7);
    border: 2px solid rgba(100, 150, 255, 0.3);
    transform-style: preserve-3d;
    perspective: 1000px;
    filter: drop-shadow(0 0 20px rgba(0, 100, 255, 0.2));
}

#discard-pile::after {
    content: attr(data-count);
    position: absolute;
    bottom: -15px;
    right: -15px;
    background: linear-gradient(135deg, rgba(100, 40, 180, 0.9), rgba(60, 20, 120, 0.9));
    color: white;
    font-weight: bold;
    font-size: 1.1em;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 20px rgba(0,0,0,0.4), 0 0 30px rgba(100, 50, 200, 0.4);
    border: 2px solid rgba(255,255,255,0.4);
    z-index: 10;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-shadow: 0 0 10px rgba(150, 100, 255, 0.7);
    letter-spacing: 1px;
    animation: pulseDiscardCount 2s infinite alternate;
}

@keyframes pulseDiscardCount {
    0% { transform: scale(1); box-shadow: 0 8px 20px rgba(0,0,0,0.4), 0 0 30px rgba(100, 50, 200, 0.3); }
    100% { transform: scale(1.2); box-shadow: 0 12px 25px rgba(0,0,0,0.5), 0 0 40px rgba(100, 50, 200, 0.5); }
}

#discard-pile:hover {
    transform: translateY(-12px) rotateY(-8deg) scale(1.05);
    box-shadow: 0 15px 30px rgba(0,0,0,0.4), 0 0 50px rgba(0, 100, 255, 0.3);
    background: linear-gradient(135deg, rgba(40, 50, 70, 0.9), rgba(25, 35, 50, 0.9));
}

#discard-pile:hover::after {
    transform: scale(1.3);
    box-shadow: 0 15px 30px rgba(0,0,0,0.5), 0 0 50px rgba(100, 50, 200, 0.6);
    background: linear-gradient(135deg, rgba(120, 50, 200, 0.9), rgba(80, 30, 150, 0.9));
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
}

#discard-pile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
    border-radius: 10px;
    pointer-events: none;
    z-index: 1;
}




/* --- Stili Drag and Drop --- */

.card[draggable="true"] {
    cursor: grab; /* Cursore per indicare che è trascinabile */
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card[draggable="true"]:hover {
    transform: translateY(-5px) scale(1.05) rotateZ(2deg);
    box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 40px rgba(0, 100, 255, 0.3);
    z-index: 50;
}

.card.dragging {
    opacity: 0.8;
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 50px rgba(0, 100, 255, 0.4);
    cursor: grabbing; /* Cursore durante il trascinamento */
    z-index: 1000; /* Assicura che sia sopra gli altri elementi */
    filter: brightness(1.2);
}

/* Stile per carte appena posizionate in multiplayer */
.card.card-placed {
    transition: opacity 0.3s ease;
    pointer-events: none;
    animation: cardPlacedFade 0.6s ease-out;
}

@keyframes cardPlacedFade {
    0% { 
        opacity: 0.8;
        transform: scale(1.1);
    }
    100% { 
        opacity: 0.7;
        transform: scale(1);
    }
}


.cell.drag-over,
.vertex.drag-over {
    background-color: rgba(50, 220, 100, 0.3); /* Sfondo verde semi-trasparente */
    outline: 3px dashed rgba(50, 220, 100, 0.8); /* Bordo tratteggiato verde */
    outline-offset: -3px;
    box-shadow: inset 0 0 30px rgba(50, 220, 100, 0.4), 0 0 30px rgba(50, 220, 100, 0.4);
    transform: scale(1.05);
    z-index: 10;
    animation: dragOverPulse 1.5s infinite alternate;
}

@keyframes dragOverPulse {
    0% { box-shadow: inset 0 0 20px rgba(50, 220, 100, 0.3), 0 0 20px rgba(50, 220, 100, 0.3); }
    100% { box-shadow: inset 0 0 40px rgba(50, 220, 100, 0.5), 0 0 40px rgba(50, 220, 100, 0.5); }
}

/* Stile opzionale per indicare drop non valido (se implementato) */
.drag-invalid {
    background-color: rgba(255, 60, 60, 0.3);
    outline: 3px dashed rgba(255, 60, 60, 0.8);
    outline-offset: -3px;
    box-shadow: inset 0 0 30px rgba(255, 60, 60, 0.4), 0 0 30px rgba(255, 60, 60, 0.4);
    animation: dragInvalidPulse 1.5s infinite alternate;
}

@keyframes dragInvalidPulse {
    0% { box-shadow: inset 0 0 20px rgba(255, 60, 60, 0.3), 0 0 20px rgba(255, 60, 60, 0.3); }
    100% { box-shadow: inset 0 0 40px rgba(255, 60, 60, 0.5), 0 0 40px rgba(255, 60, 60, 0.5); }
}



/* --- Stili Area Loop --- */

#loops-info-area {
    background-color: rgba(20, 40, 80, 0.6);
    border-radius: min(0.6vw, 10px);
    padding: min(0.8vw, 12px);
    margin-top: min(1vw, 15px);
    border: 1px solid rgba(100, 150, 255, 0.3);
    max-height: min(10vw, 150px); /* Limita altezza se ci sono molti loop */
    overflow-y: auto; /* Abilita scroll verticale se necessario */
    box-shadow: inset 0 0 20px rgba(0, 30, 60, 0.5), 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    backdrop-filter: blur(5px);
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 150, 255, 0.6) rgba(10, 20, 40, 0.3);
}

#loops-info-area::-webkit-scrollbar {
    width: 8px;
}

#loops-info-area::-webkit-scrollbar-track {
    background: rgba(10, 20, 40, 0.3);
    border-radius: 10px;
}

#loops-info-area::-webkit-scrollbar-thumb {
    background: rgba(100, 150, 255, 0.6);
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2);
}

#loops-info-area::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 150, 255, 0.8);
}

#loops-info-area h4 {
    margin: 0 0 12px 0;
    font-size: 1em;
    color: #e0f0ff;
    border-bottom: 2px solid rgba(100, 150, 255, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 0 10px rgba(100, 150, 255, 0.7);
    letter-spacing: 1px;
    position: relative;
}

#loops-info-area h4::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(100, 150, 255, 0.8), transparent);
    box-shadow: 0 0 10px rgba(100, 150, 255, 0.5);
}

#loops-info-area h4 i {
    margin-right: 8px;
    color: rgba(255, 220, 50, 0.9); /* Colore icona */
    text-shadow: 0 0 10px rgba(255, 220, 50, 0.7);
}

#loops-list {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.9em;
}

#loops-list li {
    margin-bottom: 8px;
    padding: 8px 12px;
    color: #c0d0ff;
    background-color: rgba(30, 60, 120, 0.4);
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(0, 100, 255, 0.1);
    border-left: 3px solid rgba(100, 150, 255, 0.4);
    transition: all 0.3s ease;
}

#loops-list li:hover {
    background-color: rgba(40, 80, 160, 0.5);
    transform: translateX(5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 100, 255, 0.2);
}

#loops-list li i {
    margin-right: 8px;
    width: 14px; /* Allinea icone */
    text-align: center;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Colori specifici per tipo di loop (opzionale) */
.loop-symbolic i {
    color: rgba(50, 150, 255, 0.9); /* Blu per simbolico */
    text-shadow: 0 0 10px rgba(50, 150, 255, 0.7);
}

.loop-hybrid i {
    color: rgba(180, 50, 255, 0.9); /* Viola per ibrido */
    text-shadow: 0 0 10px rgba(180, 50, 255, 0.7);
}

/* Stili per le celle con loop */
.cell.loop-highlight {
    position: relative;
    overflow: visible;
}

.cell.loop-highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    z-index: 5;
    pointer-events: none;
    animation: loopPulse 2s infinite alternate;
}

.cell.loop-symbolic::after {
    border: 2px dashed rgba(50, 150, 255, 0.8);
    box-shadow: inset 0 0 20px rgba(50, 150, 255, 0.3), 0 0 20px rgba(50, 150, 255, 0.3);
    background: repeating-linear-gradient(45deg, rgba(50, 150, 255, 0.1), rgba(50, 150, 255, 0.1) 10px, rgba(50, 150, 255, 0.2) 10px, rgba(50, 150, 255, 0.2) 20px);
}

.cell.loop-numeric::after {
    border: 2px dashed rgba(255, 150, 50, 0.8);
    box-shadow: inset 0 0 20px rgba(255, 150, 50, 0.3), 0 0 20px rgba(255, 150, 50, 0.3);
    background: repeating-linear-gradient(45deg, rgba(255, 150, 50, 0.1), rgba(255, 150, 50, 0.1) 10px, rgba(255, 150, 50, 0.2) 10px, rgba(255, 150, 50, 0.2) 20px);
}

.cell.loop-hybrid::after {
    border: 2px dashed rgba(180, 50, 255, 0.8);
    box-shadow: inset 0 0 20px rgba(180, 50, 255, 0.3), 0 0 20px rgba(180, 50, 255, 0.3);
    background: repeating-linear-gradient(45deg, rgba(180, 50, 255, 0.1), rgba(180, 50, 255, 0.1) 10px, rgba(180, 50, 255, 0.2) 10px, rgba(180, 50, 255, 0.2) 20px);
}

.cell.loop-highlight-active::after {
    animation: loopHighlight 0.8s infinite alternate;
    z-index: 10;
}

@keyframes loopPulse {
    0% { opacity: 0.5; }
    100% { opacity: 0.8; }
}

@keyframes loopHighlight {
    0% { opacity: 0.7; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.1); }
}



@keyframes homepageGlow {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* Barra laterale */
.sidebar {
    width: 240px;
    height: 100%;
    background-color: rgba(10, 20, 35, 0.95);
    color: #e0f0ff;
    display: flex;
    flex-direction: column;
    padding: 25px 0;
    box-shadow: 5px 0 25px rgba(0,0,0,0.5), 0 0 50px rgba(0, 30, 60, 0.5);
    z-index: 10;
    border-right: 1px solid rgba(100, 150, 255, 0.3);
    background-image: linear-gradient(to bottom, rgba(30, 60, 120, 0.2), transparent, rgba(30, 60, 120, 0.2));
    backdrop-filter: blur(10px);
    position: fixed;
    left: 0;
    top: 0;
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.2;
    pointer-events: none;
    z-index: -1;
}

.logo {
    display: flex;
    align-items: center;
    padding: 0 25px;
    margin-bottom: 40px;
}

.logo .logo-image {
    width: 60px;
    height: auto;
    margin-right: 15px;
    transition: all 0.3s ease;
    animation: logoIconPulse 3s infinite alternate;
}

@keyframes logoIconPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

.sidebar nav {
    flex: 1;
    margin-top: 20px;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar li {
    padding: 14px 25px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 5px 0;
    border-left: 0px solid rgba(50, 220, 100, 0);
    color: #c0d0ff;
    position: relative;
    overflow: hidden;
}

.sidebar li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.2), transparent);
    transition: width 0.3s ease;
    z-index: -1;
}

.sidebar li:hover {
    background-color: rgba(30, 60, 120, 0.3);
    color: #e0f0ff;
    transform: translateX(5px);
    border-left: 4px solid rgba(50, 220, 100, 0.6);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(50, 220, 100, 0.2);
}

.sidebar li:hover::before {
    width: 100%;
}

.sidebar li.active {
    background-color: rgba(30, 60, 120, 0.5);
    border-left: 4px solid rgba(50, 220, 100, 0.9);
    color: #e0f0ff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(50, 220, 100, 0.3);
}

.sidebar li i {
    margin-right: 15px;
    font-size: 20px;
    width: 26px;
    text-align: center;
    color: rgba(50, 220, 100, 0.8);
    text-shadow: 0 0 10px rgba(50, 220, 100, 0.5);
    transition: all 0.3s ease;
}

.sidebar li:hover i {
    transform: scale(1.2);
    color: rgba(50, 220, 100, 1);
    text-shadow: 0 0 15px rgba(50, 220, 100, 0.8);
}

.sidebar-footer {
    padding: 25px;
    border-top: 1px solid rgba(100, 150, 255, 0.2);
    margin-top: 20px;
    position: relative;
}

.sidebar-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(100, 150, 255, 0.4), transparent);
    box-shadow: 0 0 10px rgba(100, 150, 255, 0.3);
}

.sidebar-footer button {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    background-color: rgba(30, 60, 120, 0.3);
    border: 2px solid rgba(50, 220, 100, 0.4);
    color: #e0f0ff;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(50, 220, 100, 0.1);
    position: relative;
    overflow: hidden;
}

.sidebar-footer button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease-out;
}

.sidebar-footer button:hover {
    background-color: rgba(30, 60, 120, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(50, 220, 100, 0.2);
    border-color: rgba(50, 220, 100, 0.7);
}

.sidebar-footer button:hover::before {
    opacity: 1;
    transform: scale(1);
}

.sidebar-footer button:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.sidebar-footer button i {
    margin-right: 10px;
    font-size: 18px;
    color: rgba(50, 220, 100, 0.9);
    text-shadow: 0 0 10px rgba(50, 220, 100, 0.5);
    transition: all 0.3s ease;
}

.sidebar-footer button:hover i {
    transform: scale(1.2);
    color: rgba(50, 220, 100, 1);
    text-shadow: 0 0 15px rgba(50, 220, 100, 0.8);
}

/* Contenuto principale */




/* Sezione Hero con tabellone e pulsanti */
.hero-section {
    display: flex;
    align-items: stretch; /* Cambiato da center a stretch per permettere ai figli di espandersi */
    background: linear-gradient(to right, rgba(10, 20, 35, 0.8), rgba(20, 40, 70, 0.8));
    border-radius: 20px;
    overflow: hidden;

    box-shadow: 0 20px 50px rgba(0,0,0,0.5), 0 0 100px rgba(0, 100, 255, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.3);
    backdrop-filter: blur(5px);
    position: relative;
    /* Rimosso max-height per evitare il taglio dell'immagine */
    width: 100%; /* Larghezza percentuale per una migliore responsività */
    margin-left: auto; /* Centra la hero section */
    margin-right: auto; /* Centra la hero section */
    transform: perspective(1500px) rotateX(2deg);
    transition: all 0.5s ease-out;
    gap: 0;
}

.hero-section:hover {
    box-shadow: 0 25px 60px rgba(0,0,0,0.6), 0 0 120px rgba(0, 100, 255, 0.5);
    transform: perspective(1500px) rotateX(0deg) translateY(-5px);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
    pointer-events: none;
    z-index: 1;
}

.board-preview {
    flex: 0 0 50%; /* Esattamente il 50% della larghezza */
    width: 50%; /* Garantisce che occupi esattamente il 50% */
    max-width: none; /* Rimuove il limite massimo */
    min-width: 0; /* Rimuove il limite minimo */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(10, 15, 25, 0.7);
    position: relative;
    overflow: visible;
    height: auto; /* Altezza automatica per adattarsi al contenuto */
    /* Padding per non far toccare l'immagine ai bordi */
    box-sizing: border-box;
    padding-left: 0px;
    padding-right: 0px;
    align-self: center; /* Centra verticalmente nel contenitore padre */
}

/* Non forziamo aspect-ratio card per il tabellone, ma lo manteniamo quadrato o come da immagine */
/* @media screen and (min-width: 992px) {
    .board-preview {
        aspect-ratio: 1/1.46;
        height: 100%;
    }
} */

.board-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 92%;
    height: 92%;
    max-width: 310px;
    max-height: 310px;
    background: radial-gradient(circle at center, rgba(0, 100, 255, 0.2) 0%, transparent 70%);
    pointer-events: none;
    z-index: 0;
    /* Rimossa l'animazione: animation: boardPreviewGlow 5s infinite alternate; */
    border-radius: 10px;
}

@keyframes boardPreviewGlow {
    0% { opacity: 0.5; transform: scale(0.98); box-shadow: 0 0 15px rgba(0, 100, 255, 0.2); }
    100% { opacity: 0.6; transform: scale(1.01); box-shadow: 0 0 20px rgba(0, 120, 255, 0.25); }
}

.tabellone-image {
    width: 100%;
    height: auto; /* Altezza automatica per mantenere le proporzioni */
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: none; /* Rimuove il limite di altezza massima */
    padding: 0; /* Rimuove il padding che potrebbe causare il taglio */
}

.tabellone-img {
    width: auto; /* Larghezza automatica per mantenere le proporzioni */
    height: auto; /* Altezza automatica per mantenere le proporzioni */
    object-fit: contain; /* Mantiene l'immagine intera senza tagliarla */
    border-radius: 8px;
    border: 3px solid rgba(30, 60, 120, 0.7);
    box-shadow: 0 10px 25px rgba(0,0,0,0.6), 0 0 30px rgba(0, 100, 255, 0.2);
    filter: drop-shadow(0 0 15px rgba(0, 100, 255, 0.3));
    position: relative;
    z-index: 3;
    max-width: 100%; /* Limita la larghezza al 100% del contenitore */
    max-height: none; /* Rimuove il limite di altezza massima */
    display: block; /* Assicura che l'immagine sia visualizzata come blocco */
}

/* Maintain aspect ratio for desktop and larger screens */
@media (min-width: 992px) {
    .tabellone-image {
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0; /* Rimuove il padding che potrebbe causare il taglio */
    }

    .tabellone-img {
        width: auto; /* Larghezza automatica per mantenere le proporzioni */
        height: auto; /* Altezza automatica per mantenere le proporzioni */
        object-fit: contain; /* Mantiene l'immagine intera senza tagliarla */
        max-width: 100%;
        max-height: none; /* Rimuove il limite di altezza massima */
    }
}

@keyframes tabelloneGlow {
    0% { filter: drop-shadow(0 0 10px rgba(0, 100, 255, 0.2)); }
    50% { filter: drop-shadow(0 0 15px rgba(50, 220, 100, 0.3)); }
    100% { filter: drop-shadow(0 0 10px rgba(0, 100, 255, 0.2)); }
}

/* Rimosso l'effetto hover che ingrandisce l'immagine */

.hero-content {
    flex: 0 0 50%; /* Esattamente il 50% della larghezza */
    width: 50%; /* Garantisce che occupi esattamente il 50% */
    display: flex; /* Aggiunto display: flex per abilitare il centering verticale */
    flex-direction: column;
    justify-content: center; /* Centra verticalmente il contenuto */
    align-items: center; /* Centra orizzontalmente il contenuto */
    padding: 10px 20px; /* Padding per più respiro */
    text-align: center; /* Centra il testo */
    box-sizing: border-box;
    height: 100%; /* Occupa tutta l'altezza disponibile */
}

/* Aggiungo stile per hero-title-container */
.hero-title-container {
    text-align: center;
    width: 90%;
    margin-bottom: 30px;
}

/* Aggiungo stile per guest-title-view */
#guest-title-view {
    text-align: center;
    width: 100%;
}

.hero-content h1 {
    font-size: 2.8em;
    margin: 0 0 25px 0; /* Aumentato il margine inferiore */
    color: #e0f0ff;
    text-shadow: 0 0 20px rgba(100, 150, 255, 0.7), 0 0 40px rgba(0, 100, 255, 0.4);
    letter-spacing: 2px;
    font-weight: 800;
    position: relative;
    display: inline-block;
    text-align: center;
}

.hero-content h1::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 160px;
    height: 3px;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.8), rgba(30, 150, 70, 0.8));
    box-shadow: 0 0 15px rgba(50, 220, 100, 0.6);
    z-index: 2;
}

.hero-content h2 {
    font-size: 1.3em;
    margin: 0 0 40px 0;
    font-weight: normal;
    color: rgba(220, 230, 255, 0.9);
    text-shadow: 0 0 10px rgba(0, 30, 60, 0.5);
    letter-spacing: 0.5px;
    line-height: 1.4;
    text-align: center;
    width: 100%;
}

.stats-container {
    display: flex;
    margin-bottom: 30px;
    gap: 20px;
    max-width: 100%;
}

.stat-item {
    background-color: rgba(30, 60, 120, 0.4);
    border-left: 4px solid rgba(50, 220, 100, 0.8);
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 100, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4), 0 0 40px rgba(0, 100, 255, 0.3);
    background-color: rgba(40, 80, 160, 0.5);
    border-left-width: 6px;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
    pointer-events: none;
    z-index: 1;
}

.stat-value {
    display: block;
    font-size: 2em;
    font-weight: bold;
    color: #e0f0ff;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.7);
    margin-bottom: 5px;
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 0.95em;
    color: rgba(200, 220, 255, 0.9);
    text-shadow: 0 0 8px rgba(0, 30, 60, 0.5);
    position: relative;
    z-index: 2;
    letter-spacing: 0.5px;
}

.hero-actions-container {
    display: flex;
    width: 100%;
    flex-direction: row;
    gap: 40px;
    margin-bottom: 60px;
    align-items: flex-start;
    margin-top: 25px;
}

.play-buttons {
    display: flex;
    flex-direction: column;
    gap: 25px; /* Ridotto lo spazio tra i pulsanti */
    margin-bottom: 0;
    width: 100%;
    align-items: center;
    margin-top: 2px;
    justify-content: center;
    height: auto;
}

.play-buttons .primary-btn {
    width: 100%; /* I pulsanti occupano tutta la larghezza disponibile nel loro contenitore */
    max-width: 400px; /* Massimo controllo sulla larghezza */
    padding: 18px 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Allineato a sinistra */
    gap: 15px;
    text-align: left; /* Testo allineato a sinistra */
    font-size: 1.2em; /* Font di base */
    margin-bottom: 0; /* Tolto il margine inferiore per usare solo il gap */
    font-weight: 600; /* Testo leggermente più in grassetto */
}

/* Stile specifico per i pulsanti Nuova Partita e Sfida un Amico */
#new-game-button, #start-local-button {
    font-size: 1.3em; /* Font size leggermente più grande */
}

/* Stili per contenuto pulsante con testo primario e secondario */
.button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Allineato a sinistra */
    justify-content: center;
    width: 100%; /* Occupa tutta la larghezza disponibile */
}

.primary-text {
    font-size: 1.25em; /* Aumentato da 1.1em */
    font-weight: 600;
    margin-bottom: 4px; /* Ridotto lo spazio tra testo primario e secondario */
    text-align: left; /* Testo allineato a sinistra */
}

.secondary-text {
    font-size: 0.75em;
    font-weight: normal;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.1; /* Ridotta l'altezza della linea */
    text-align: left; /* Testo allineato a sinistra */
}

.play-buttons .primary-btn i {
    font-size: 1.6em; /* Icone più grandi */
    width: 36px; /* Larghezza aumentata */
    text-align: center;
    margin-right: 0; /* Rimosso margine a destra perché abbiamo già il gap */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Icone più grandi per i pulsanti con testo più grande */
#new-game-button i, #start-local-button i {
    font-size: 1.8em; /* Icone ancora più grandi per pulsanti specifici */
    width: 40px; /* Larghezza aumentata */
}

/* Effetto 3D per il pulsante Gioca Online nella hero non loggata */
#new-game-button {
    transform: translateY(-1px); /* Spostamento verso l'alto per effetto 3D */
    box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.2), /* Ombra principale per profondità */
        0 1px 3px rgba(0, 0, 0, 0.1), /* Ombra leggera per bordi */
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Highlight interno superiore */
    border: none; /* Rimuove il bordo di default */
    border-top: 1px solid rgba(255, 255, 255, 0.3); /* Highlight superiore */
    border-bottom: 4px solid rgba(0, 50, 0, 0.25); /* Bordo inferiore più spesso per maggiore 3D */
    transition: all 0.2s ease; /* Transizione fluida */
    margin-bottom: 8px; /* Piccolo spazio aggiuntivo sotto il pulsante */
    background: linear-gradient(135deg, #2a8e4c 0%, #1f7a3f 100%); /* Verde più profondo */
}

/* Effetto hover per accentuare la tridimensionalità */
#new-game-button:hover {
    transform: translateY(-3px); /* Più in alto quando hover */
    box-shadow:
        0 6px 10px rgba(0, 0, 0, 0.25), /* Ombra più pronunciata */
        0 2px 4px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-bottom: 4px solid rgba(0, 50, 0, 0.4); /* Bordo inferiore più scuro su hover */
}

/* Effetto press quando cliccato */
#new-game-button:active {
    transform: translateY(2px); /* Affonda di più quando premuto */
    box-shadow:
        0 2px 3px rgba(0, 0, 0, 0.15), /* Ombra più piccola */
        inset 0 1px 2px rgba(0, 0, 0, 0.1); /* Ombra interna per effetto premuto */
    border-bottom: 1px solid rgba(0, 50, 0, 0.25); /* Bordo inferiore quasi scompare */
}

/* Effetto 3D per il pulsante Allenamento nella hero non loggata */
#start-local-button {
    transform: translateY(-1px); /* Spostamento verso l'alto per effetto 3D */
    box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.2), /* Ombra principale per profondità */
        0 1px 3px rgba(0, 0, 0, 0.1), /* Ombra leggera per bordi */
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Highlight interno superiore */
    border: none; /* Rimuove il bordo di default */
    border-top: 1px solid rgba(255, 255, 255, 0.3); /* Highlight superiore */
    border-bottom: 4px solid rgba(25, 25, 80, 0.3); /* Bordo inferiore più spesso per maggiore 3D */
    transition: all 0.2s ease; /* Transizione fluida */
    background: linear-gradient(135deg, #3a7bd5 0%, #2c5ea0 100%); /* Blu più profondo */
}

/* Effetto hover per accentuare la tridimensionalità */
#start-local-button:hover {
    transform: translateY(-3px); /* Più in alto quando hover */
    box-shadow:
        0 6px 10px rgba(0, 0, 0, 0.25), /* Ombra più pronunciata */
        0 2px 4px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    border-bottom: 4px solid rgba(25, 25, 80, 0.45); /* Bordo inferiore più scuro su hover */
}

/* Effetto press quando cliccato */
#start-local-button:active {
    transform: translateY(2px); /* Affonda di più quando premuto */
    box-shadow:
        0 2px 3px rgba(0, 0, 0, 0.15), /* Ombra più piccola */
        inset 0 1px 2px rgba(0, 0, 0, 0.1); /* Ombra interna per effetto premuto */
    border-bottom: 1px solid rgba(25, 25, 80, 0.25); /* Bordo inferiore quasi scompare */
}

/* Stili per i bottoni di social login */
.social-login-divider {
    text-align: center;
    position: relative;
    margin: 20px 0;
}

.social-login-divider::before,
.social-login-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
}

.social-login-divider::before {
    left: 0;
}

.social-login-divider::after {
    right: 0;
}

.social-login-divider span {
    background-color: #fff;
    padding: 0 15px;
    font-size: 0.9rem;
    color: #777;
}

.social-login-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
}

.social-login-btn {
    width: 100%;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #fff;
    color: #333;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-btn {
    background-color: #fff;
    color: #444;
}

.google-btn i {
    color: #4285F4;
    font-size: 1.2rem;
}

.social-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.social-login-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.feature-boxes {
    display: flex;
    flex-direction: row;
    gap: 15px;
    width: 70%;
    height: auto;
    align-self: stretch;
}

.feature-box {
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.75), rgba(10, 20, 40, 0.75));
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.6), 0 0 50px rgba(0, 100, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 3px solid rgba(100, 150, 255, 0.35);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: calc(33.33% - 10px);
    align-items: center;
    text-align: center;
    height: 355px;
    justify-content: center;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.5), 0 0 40px rgba(0, 120, 255, 0.2);
    border: 1px solid rgba(100, 180, 255, 0.35);
    background: linear-gradient(135deg, rgba(30, 50, 100, 0.7), rgba(15, 30, 60, 0.7));
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.4), 0 0 40px rgba(0, 150, 255, 0.2);
    border-color: rgba(50, 220, 100, 0.3);
}

.feature-image {
    width: 130px;
    height: 180px;
    overflow: visible;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    position: relative;
    box-shadow: 0 12px 25px rgba(0,0,0,0.5), 0 0 35px rgba(0, 100, 255, 0.3);
    border: 3px solid rgba(100, 150, 255, 0.35);
    background: rgba(10, 30, 60, 0.65);
    margin: 0 0 10px 0;
    padding: 2px;
}

.feature-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0.1));
    border-radius: 10px;
    z-index: 1;
    pointer-events: none;
}

.feature-img {
    max-width: 100%;
    height: auto;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
    transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform: scale(0.95);
    filter: brightness(1.1) contrast(1.1);
}

.feature-box:hover .feature-img {
    transform: scale(1.05);
    filter: brightness(1.2) contrast(1.2);
}

.feature-box .text-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    align-items: center;
}

.feature-box h3 {
    font-size: 1.4em;
    margin: 0 0 5px 0;
    color: #e8f4ff;
    text-shadow: 0 0 15px rgba(0, 120, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.feature-box p {
    font-size: 1.1em;
    color: rgba(220, 230, 255, 0.95);
    margin: 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 90%;
}

/* Label per "Disponibile Prossimamente!" */
.feature-box .coming-soon-label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    font-weight: bold;
    font-size: 1.2em;
    border-radius: 20px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.feature-box.active .coming-soon-label {
    opacity: 1;
}

.primary-btn, .secondary-btn {
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.35);
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 280px;
    text-align: left;
    flex-shrink: 0;
}

/* Stili per la schermata di inserimento nomi */
.names-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25px;
}

/* Stili per i cerchietti colorati dei giocatori */
.player-color-circle {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.player-color-circle.white {
    background-color: #ffffff;
}

.player-color-circle.black {
    background-color: #000000;
}

.names-container h2 {
    color: #2c3e50;
    font-size: 1.8em;
    margin: 0 0 10px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.names-info {
    color: #34495e;
    font-size: 1em;
    margin: 0 0 15px 0;
    text-align: center;
    font-style: italic;
    background-color: rgba(52, 152, 219, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    border-left: 3px solid #3498db;
}

.player-name-input {
    width: 100%;
    max-width: 400px;
    text-align: left;
}

.player-name-input label {
    display: block;
    margin-bottom: 8px;
    color: #34495e;
    font-weight: 600;
    font-size: 1.1em;
}

.player-name-input label i {
    margin-right: 8px;
    color: #3498db;
}

.player-name-input input {
    width: 100%;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    font-size: 1.1em;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.player-name-input input:focus {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
    outline: none;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9em;
    margin-top: -15px;
    text-align: center;
    min-height: 20px;
}

.names-buttons {
    display: flex;
    gap: 15px;
    margin-top: 10px;
    justify-content: center;
}

.primary-btn::before, .secondary-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    /* Rimuovo transition per evitare flash */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.primary-btn {
    background: linear-gradient(135deg, rgba(50, 220, 100, 0.9), rgba(30, 150, 70, 0.9));
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 40px rgba(50, 220, 100, 0.4);
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, box-shadow;
    position: relative;
    overflow: hidden;
}

.online-btn {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.9), rgba(25, 118, 210, 0.9));
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 40px rgba(33, 150, 243, 0.4);
}

.online-btn i {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    font-size: 1.4em;
}

.primary-btn:hover {
    transform: translateY(-7px) scale(1.08);
    box-shadow: 0 18px 35px rgba(0, 0, 0, 0.45), 0 0 70px rgba(50, 220, 100, 0.7);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                box-shadow 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.primary-btn:hover::before {
    opacity: 1;
    transform: scale(1);
    /* Aggiungo transizione solo su hover per evitare flash */
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.primary-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.secondary-btn {
    background: linear-gradient(135deg, rgba(30, 120, 255, 0.9), rgba(20, 80, 200, 0.9));
    color: #e0f0ff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 100, 255, 0.4);
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: transform, box-shadow;
    position: relative;
    overflow: hidden;
}

.secondary-btn:hover {
    transform: translateY(-7px) scale(1.08);
    box-shadow: 0 18px 35px rgba(0, 0, 0, 0.45), 0 0 70px rgba(0, 100, 255, 0.7);
    transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
                box-shadow 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.secondary-btn:hover::before {
    opacity: 1;
    transform: scale(1);
    /* Aggiungo transizione solo su hover per evitare flash */
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.secondary-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.primary-btn i, .secondary-btn i {
    margin-right: 15px;
    font-size: 1.5em;
    transition: transform 0.3s ease;
    position: relative;
    z-index: 2;
    min-width: 30px;
    text-align: center;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.primary-btn:hover i, .secondary-btn:hover i {
    transform: scale(1.2) rotate(10deg);
}

.status-message {
    min-height: 30px;
    color: rgba(255, 220, 50, 0.9);
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 220, 50, 0.7);
    padding: 8px 15px;
    border-radius: 8px;
    background-color: rgba(40, 30, 10, 0.3);
    display: inline-block;
    margin-top: 10px;
    transition: all 0.3s ease;
    animation: statusPulse 2s infinite alternate;
}

@keyframes statusPulse {
    0% { text-shadow: 0 0 10px rgba(255, 220, 50, 0.5); }
    100% { text-shadow: 0 0 20px rgba(255, 220, 50, 0.8); }
}

/* Sezione regole */
.rules-section {
    background: linear-gradient(135deg, rgba(20, 40, 70, 0.8), rgba(10, 20, 35, 0.8));
    border-radius: 20px;
    padding: 50px;
    margin: 0 auto 50px auto; /* Centrata con margini automatici */
    box-shadow: 0 20px 50px rgba(0,0,0,0.5), 0 0 100px rgba(0, 100, 255, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.3);
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: perspective(1500px) rotateX(-2deg);
    z-index: 2;
    max-width: 80%; /* Limitata a una percentuale della larghezza disponibile */
}

.rules-section:hover {
    box-shadow: 0 25px 60px rgba(0,0,0,0.6), 0 0 120px rgba(0, 100, 255, 0.5);
    transform: perspective(1500px) rotateX(0deg) translateY(-5px);
}

.rules-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 60%);
    pointer-events: none;
    z-index: 1;
}

.section-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.section-header h2 {
    font-size: 2.4em;
    margin: 0 0 20px 0;
    color: #e0f0ff;
    text-shadow: 0 0 20px rgba(100, 150, 255, 0.7), 0 0 40px rgba(0, 100, 255, 0.4);
    letter-spacing: 2px;
    font-weight: 800;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.8), rgba(30, 150, 70, 0.8));
    box-shadow: 0 0 15px rgba(50, 220, 100, 0.6);
    z-index: 2;
}

.section-header p {
    font-size: 1.2em;
    color: rgba(220, 230, 255, 0.9);
    margin: 0;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    text-shadow: 0 0 10px rgba(0, 30, 60, 0.5);
}

.rules-content {
    display: flex;
    align-items: center;
    gap: 40px;
    justify-content: space-between;
    margin-top: 30px;
}

.rules-image, .rules-video {
    flex: 1;
    position: relative;
}

.rules-image img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.4);
}

.rules-video {
    flex: 1;
    text-align: center;
    margin-right: 20px;
}

.rules-video iframe {
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.5), 0 0 40px rgba(0, 100, 255, 0.2);
    border: 2px solid rgba(100, 150, 255, 0.25);
    max-width: 100%;
}

.rules-text {
    flex: 1;
}

blockquote {
    border-left: 4px solid #4CAF50;
    padding-left: 20px;
    margin: 0 0 20px 0;
    font-style: italic;
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
}

blockquote cite {
    display: block;
    margin-top: 10px;
    font-size: 0.9em;
    color: rgba(255,255,255,0.7);
}

.rules-text p {
    line-height: 1.6;
    margin-bottom: 25px;
    color: rgba(255,255,255,0.9);
}

.learn-more-btn {
    background-color: transparent;
    border: 2px solid #4CAF50;
    color: #4CAF50;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.learn-more-btn:hover {
    background-color: #4CAF50;
    color: white;
}

/* Sezione features */
.features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin: 0 auto 40px auto; /* Centrata con margini automatici */
    position: relative;
    z-index: 2;
    max-width: 80%;
}


/* Media Queries per Hero Section Responsiveness */
@media (max-width: 1100px) {
    .hero-section {
        flex-direction: column;
        padding: 30px; /* Padding per mobile */
        max-height: none; /* Rimuove l'altezza massima per permettere l'espansione verticale */
        width: 95%; /* Occupa più larghezza su schermi medi */
        transform: perspective(1500px) rotateX(0deg); /* Rimuove l'effetto 3D su mobile */
    }

    .board-preview {
        flex: 0 0 auto; /* Rimuove la base flessibile */
        width: 70%; /* Larghezza percentuale per l'immagine su mobile */
        max-width: 400px; /* Limita la dimensione massima dell'immagine */
        margin-bottom: 30px; /* Spazio sotto l'immagine */
        padding: 15px;
        min-width: auto; /* Rimuove il minimo ingombro su mobile */
        height: auto; /* Altezza automatica per mantenere le proporzioni */
    }

    .tabellone-image {
        width: 100%;
        height: auto;
        padding: 0;
    }

    .tabellone-img {
        width: 100%;
        height: auto;
        object-fit: contain;
    }

    .hero-content {
        flex: 0 0 auto; /* Rimuove la base flessibile */
        width: 100%; /* Occupa tutta la larghezza disponibile su mobile */
        padding: 20px; /* Padding ridotto per mobile */
        align-items: center;
        display: flex; /* Mantiene il display flex */
        justify-content: center; /* Mantiene il centering verticale */
        height: auto; /* Altezza automatica su mobile */
    }

    .hero-content h1 {
        font-size: 2.2em; /* Font più piccolo per h1 */
        margin-bottom: 15px;
    }

    .play-buttons {
        width: 100%;
        max-width: 400px; /* Limita la larghezza massima dei pulsanti */
        margin: 0 auto; /* Centra i pulsanti */
    }
    .hero-content h1::after {
        width: 120px; /* Sottolineatura più corta */
    }

    .hero-content h2 {
        font-size: 1.1em; /* Font più piccolo per h2 */
        margin-bottom: 25px;
    }

    .hero-actions-container {
        flex-direction: column;
        align-items: center; /* Centra i pulsanti e le feature boxes */
        gap: 20px; /* Spazio ridotto */
        width: 100%;
    }

    .play-buttons {
        gap: 15px; /* Spazio tra i pulsanti */
        width: 100%;
        align-items: center; /* Centra i pulsanti */
    }

    .play-buttons .primary-btn {
        width: 100%; /* Pulsanti a larghezza piena */
        max-width: 350px; /* Limita la larghezza dei pulsanti */
        padding: 15px 20px;
        font-size: 1em;
        justify-content: center; /* Centra il contenuto del pulsante */
        text-align: center;
    }
    .play-buttons .primary-btn .button-content {
        align-items: center; /* Centra testo primario e secondario */
    }
    .play-buttons .primary-btn .primary-text {
        text-align: center;
    }
    .play-buttons .primary-btn .secondary-text {
        text-align: center;
    }


    .feature-boxes {
        flex-direction: column; /* Feature boxes in colonna */
        width: 100%;
        max-width: 350px; /* Limita larghezza */
        gap: 15px;
    }

    .feature-box {
        width: 100%; /* Larghezza piena */
        height: auto; /* Altezza automatica */
        padding: 15px;
    }
    .feature-image {
        width: 100px; /* Immagine più piccola */
        height: 146px; /* Mantiene aspect ratio */
    }
}

@media (max-width: 768px) {
    #homepage {
        padding-left: 0; /* Rimuove padding per la sidebar su mobile */
    }
    .main-content {
        padding: 15px; /* Padding generale per il contenuto principale */
    }
    .hero-section {
        width: 100%; /* Occupa tutta la larghezza disponibile */
        padding: 20px;
        margin-bottom: 30px; /* Più spazio sotto la hero */
        border-radius: 15px; /* Bordo più arrotondato su mobile */
    }
    .board-preview {
        width: 70%;
        max-width: 250px;
        margin-top: 10px; /* Aggiunge spazio sopra il tabellone */
    }
    .hero-content h1 {
        font-size: 1.8em;
    }
    .hero-content h2 {
        font-size: 1em;
        margin-bottom: 25px; /* Riduce lo spazio sotto il sottotitolo */
    }
    .play-buttons {
        gap: 15px; /* Riduce lo spazio tra i pulsanti */
    }
    .play-buttons .primary-btn {
        max-width: 300px;
        font-size: 0.9em;
        padding: 15px; /* Riduce il padding dei pulsanti */
    }
    .rules-section, .features-section, .academy-title, .academy-subtitle {
        max-width: 100%; /* Sezioni a larghezza piena */
        padding: 20px;
    }
    .section-header h2, .academy-title {
        font-size: 1.8em;
    }
    .section-header p, .academy-subtitle {
        font-size: 1em;
    }
    .rules-video iframe {
        height: auto; /* Altezza automatica per il video */
        aspect-ratio: 16/9; /* Mantiene l'aspect ratio del video */
    }
    .features-section {
        grid-template-columns: 1fr; /* Una colonna per le feature cards */
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 15px 10px; /* Riduce ulteriormente il padding orizzontale */
    }
    .hero-content h1 {
        font-size: 1.5em; /* Riduce ulteriormente la dimensione del titolo */
        margin-bottom: 12px; /* Riduce lo spazio sotto il titolo */
    }
    .hero-content h1::after {
        width: 80px; /* Sottolineatura ancora più corta */
        height: 2px; /* Sottolineatura più sottile */
    }
    .hero-content h2 {
        font-size: 0.85em; /* Riduce ulteriormente la dimensione del sottotitolo */
        margin-bottom: 20px; /* Riduce lo spazio sotto il sottotitolo */
    }
    .board-preview {
        width: 85%; /* Aumenta leggermente la larghezza per schermi molto piccoli */
        max-width: 180px; /* Riduce la dimensione massima */
        padding: 5px;
        margin-bottom: 20px; /* Riduce lo spazio sotto il tabellone */
    }
    .hero-section {
        padding: 15px 10px; /* Riduce il padding della hero section */
        border-radius: 12px; /* Bordo ancora più arrotondato */
    }
    .play-buttons {
        gap: 12px; /* Riduce ulteriormente lo spazio tra i pulsanti */
    }
    .play-buttons .primary-btn {
        padding: 12px;
        font-size: 0.8em;
        border-radius: 8px; /* Bordi più arrotondati per i pulsanti */
    }
    .play-buttons .primary-btn i {
        font-size: 1.2em;
        width: 25px; /* Riduce lo spazio per le icone */
    }
    .play-buttons .primary-btn .primary-text {
        font-size: 0.85em;
    }
    .play-buttons .primary-btn .secondary-text {
        font-size: 0.6em;
    }

    .rules-section, .features-section {
        padding: 15px;
    }
    .section-header h2, .academy-title {
        font-size: 1.5em;
    }
    .section-header p, .academy-subtitle {
        font-size: 0.9em;
    }
}

/* Schermi molto piccoli (sotto i 360px) */
@media (max-width: 360px) {
    .hero-content h1 {
        font-size: 1.3em;
    }
    .hero-content h2 {
        font-size: 0.8em;
        margin-bottom: 15px;
    }
    .board-preview {
        max-width: 150px;
    }
    .play-buttons .primary-btn {
        padding: 10px;
    }
    .play-buttons .primary-btn i {
        font-size: 1.1em;
        width: 20px;
    }
    .play-buttons .primary-btn .primary-text {
        font-size: 0.8em;
    }
    .play-buttons .primary-btn .secondary-text {
        font-size: 0.55em;
    }
}

.feature-card:hover i {
    transform: scale(1.2);
    color: rgba(50, 220, 100, 1);
    text-shadow: 0 0 20px rgba(50, 220, 100, 0.7);
}

.feature-card:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, rgba(50, 220, 100, 0.8), rgba(30, 150, 70, 0.8));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease-out;
}

.feature-card:hover:after {
    transform: scaleX(1);
}

.feature-card i {
    font-size: 2.2em;
    color: rgba(50, 220, 100, 0.9);
    margin-bottom: 15px;
    text-shadow: 0 0 15px rgba(50, 220, 100, 0.5);
    transition: all 0.4s ease;
    display: block;
}

.feature-card h3 {
    font-size: 1.3em;
    margin: 0 0 12px 0;
    color: white;
}

.feature-card p {
    color: rgba(255,255,255,0.7);
    line-height: 1.6;
    margin: 0 0 18px 0;
}

.read-more {
    color: rgba(50, 220, 100, 0.9);
    font-size: 0.9em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-block;
}

.feature-card:hover .read-more {
    transform: translateX(5px);
    color: rgba(50, 220, 100, 1);
}

/* Responsività */
@media (max-width: 1100px) {
    .hero-section {
        flex-direction: column;
    }

    .hero-content {
        max-width: 100%;
        padding-top: 20px;
        align-items: center; /* Assicuriamo che sia centrato anche in responsive */
        text-align: center;
    }

    .primary-btn, .secondary-btn {
        width: 90%;
        max-width: 350px;
    }

    .hero-actions-container {
        flex-direction: column;
    }

    .hero-actions-container {
        flex-direction: column;
    }

    .play-buttons {
        width: 100%;
        flex-direction: column; /* Manteniamo la direzione colonna */
        justify-content: center;
        gap: 15px;
    }

    .feature-boxes {
        width: 100%;
        margin-top: 25px;
        flex-wrap: wrap;
    }

    .feature-box {
        width: calc(50% - 10px);
        padding: 20px;
        min-height: 180px;
    }

    .feature-image {
        width: 100px;
        height: 125px;
    }

    .feature-box h3 {
        font-size: 1.3em;
    }

    .feature-box p {
        font-size: 1.1em;
    }

    .board-preview {
        width: 100%;
        padding: 30px;
    }

    .rules-content {
        flex-direction: column;
    }

    .rules-video {
        width: 100%;
        margin: 0 auto 20px auto;
    }

    .rules-video iframe {
        max-width: 100%;
        height: auto;
    }
}

@media (max-width: 768px) {
    #homepage {
        flex-direction: column;
        height: auto;
        overflow-y: auto;
    }

    .main-content {

    }

    .sidebar {
        width: 240px;
        height: 100vh;
        padding: 10px;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 100;
    }

    .feature-boxes {
        margin-top: 20px;
        gap: 15px;
        flex-direction: column;
    }

    .feature-box {
        width: 100%;
        min-height: auto;
        padding: 15px;
        flex-direction: row;
        text-align: left;
        align-items: center;
        justify-content: flex-start;
    }

    .feature-box .text-content {
        align-items: flex-start;
    }

    .feature-image {
        width: 90px;
        height: 115px;
        margin: 0 15px 0 0;
    }

    .feature-box h3 {
        font-size: 1.2em;
    }

    .feature-box p {
        font-size: 0.95em;
        white-space: normal;
    }

    .primary-btn {
        width: 100%;
        max-width: none;
    }

    .logo {
        margin-bottom: 10px;
    }

    .logo .logo-image {
        width: 40px;
    }

    .sidebar nav ul {
        display: flex;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .sidebar li {
        white-space: nowrap;
    }

    .sidebar-footer {
        display: none;
    }

    .play-buttons {
        flex-direction: column;
        gap: 12px; /* Più vicini sui dispositivi mobili */
    }

    .play-buttons .primary-btn {
        padding: 15px 25px; /* Ridotto padding verticale, aumentato orizzontale per mobile */
        font-size: 1.1em;
        width: 95%; /* Ancora più largo su mobile */
        max-width: none; /* Rimuove il limite di larghezza su mobile */
        justify-content: flex-start; /* Mantiene l'allineamento a sinistra anche su mobile */
    }

    .primary-text {
        font-size: 1em;
        margin-bottom: 3px; /* Ancora meno spazio su mobile */
    }

    .secondary-text {
        font-size: 0.7em;
        line-height: 1; /* Compatto su mobile */
    }

    .primary-btn, .secondary-btn {
        padding: 20px 30px;
        font-size: 1.2em;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .features-section {
        grid-template-columns: 1fr;
    }
}

/* Stili esistenti continuano sotto */

#dice-area .dice .face .face-symbol {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: 0.8em;
    opacity: 0.7;
    color: rgba(255,255,255,0.8);
}

/* Miglioramento stile 3D con bordi e riflessi */
#dice-area .dice {
    box-shadow: 0 15px 35px rgba(0,0,0,0.25);
}

/* Test: Rimuovo riflesso ::before da tutte le facce */
/*
#dice-area .dice .face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom,
        rgba(255,255,255,0.2) 0%,
        rgba(255,255,255,0) 100%);
    border-radius: 12px 12px 0 0;
    pointer-events: none;
}
*/

/* Test: Rimuovo riflesso ::after da tutte le facce */
/*
#dice-area .dice .face::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at 30% 30%,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0) 60%
    );
    border-radius: 12px;
    opacity: 0.5;
    pointer-events: none;
}
*/
/* Personalizzazione facce per tipo di dado */
#dice-area .dice.alpha .face.front::after {
    background: radial-gradient(
        circle at 30% 30%,
        rgba(46, 204, 113, 0.4) 0%,
        rgba(46, 204, 113, 0) 70%
    );
}

#dice-area .dice.numeric .face.front::after {
    background: radial-gradient(
        circle at 30% 30%,
        rgba(52, 152, 219, 0.4) 0%,
        rgba(52, 152, 219, 0) 70%
    );
}

/* Riflessi specifici per dadi colorati - Temporaneamente commentati per debug */
/*
#dice-area .dice.color.white .face.front::after {
    background: radial-gradient(
        circle at 30% 30%,
        rgba(255, 255, 255, 0.5) 0%,
        rgba(255, 255, 255, 0) 70%
    );
}

#dice-area .dice.color.black .face.front::after {
    background: radial-gradient(
        circle at 30% 30%,
        rgba(40, 40, 40, 0.5) 0%,
        rgba(40, 40, 40, 0) 70%
    );
}
*/
/* Effetto particelle per i dadi */
.dice-particles {
    position: absolute;
    z-index: 10;
    pointer-events: none;
}

/* Migliora l'animazione per essere più realistica */
/* Rimuovo definizione duplicata di @keyframes dice-roll */

@keyframes dice-glow {
    0% { opacity: 0; transform: scale(1.2); }
    20% { opacity: 0.7; transform: scale(1.5); }
    60% { opacity: 0.3; transform: scale(1.8); }
    100% { opacity: 0; transform: scale(2); }
}

@keyframes shadow-movement {
    0% { opacity: 0; transform: scaleX(0.8); }
    20% { opacity: 0.2; transform: scaleX(1.5) translateY(-40px); }
    40% { opacity: 0.4; transform: scaleX(1.2) translateY(-20px); }
    60% { opacity: 0.6; transform: scaleX(1.4) translateY(-10px); }
    80% { opacity: 0.8; transform: scaleX(1.3) translateY(-5px); }
    100% { opacity: 0.5; transform: scaleX(1.3) translateY(0); }
}

@keyframes result-appear {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Modifiche specifiche per i dadi fisici */
#dice-area .dice {
    box-shadow: 0 15px 35px rgba(0,0,0,0.25);
}

/* Stile numerico rosso - come nell'immagine */
#dice-area .dice.numeric .face {
    background: #d12c34; /* Rosso brillante */
    font-family: 'Arial', sans-serif;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

/* Texture sottile per dare un aspetto più realistico */
#dice-area .dice.numeric .face::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 8px;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h40v40H40V0zm-40 40h40v40H0V40z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
    pointer-events: none;
}

/* Stile alfabetico blu - come nell'immagine */
#dice-area .dice.alpha .face {
    background: #2271b3; /* Blu royal */
    font-family: 'Arial', sans-serif;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

#dice-area .dice.alpha .face::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 8px;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h40v40H40V0zm-40 40h40v40H0V40z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
    pointer-events: none;
}

/* Stile per dado di colore - combinazione bianco/nero come nell'immagine */
#dice-area .dice.color .face {
    font-family: 'Arial', sans-serif;
    font-weight: 800;
    border-radius: 8px; /* Assicuriamo la coerenza con il border-radius generale */
    overflow: hidden; /* Aggiunto per evitare sconfinamenti agli angoli */
    position: relative;
}

/* Per le facce del dado di colore */
#dice-area .dice.color.white .face,
#dice-area .dice.color.black .face {
    position: relative;
    border-radius: 8px; /* Coerenza con il valore generale */
    box-shadow: inset 0 0 10px rgba(0,0,0,0.2); /* Ombra interna più morbida */
}

/* Texture sottile per dare un aspetto più realistico e rimuovere problemi agli angoli */
#dice-area .dice.color .face::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 6px; /* Leggermente minore del bordo esterno */
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h40v40H40V0zm-40 40h40v40H0V40z' fill='%23000000' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.1;
    pointer-events: none;
    z-index: 1;
}

/* Stile semplificato per G1 e G2 per evitare sovrapposizioni */
.g1, .g2 {
    /* Rimosso display flex, width, height, border-radius, position, z-index, margin, background-color */
    font-size: 70px !important; /* Dimensione fissa */
    font-weight: 900;
    font-family: 'Arial', sans-serif;
    /* Centratura gestita da flexbox del genitore .cube__face */
    transform: scale(1.25) !important; /* Scala fissa */
    transition: none !important; /* Rimuoviamo tutte le transizioni */
    animation: none !important; /* Rimuoviamo tutte le animazioni */
}

.g1 {
    color: #000000 !important;
    text-shadow: none !important;
    -webkit-text-stroke: 0px !important;
}

.g2 {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
}

/* Colori delle facce */
#dice-area .dice.color.white .face {
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    border: 2px solid rgba(200, 200, 200, 0.6);
}

#dice-area .dice.color.black .face {
    background: linear-gradient(135deg, #333333, #000000);
    border: 2px solid rgba(100, 100, 100, 0.6);
}

/* Correzioni specifiche per le facce dei cubi 3D dei dadi */
.color-cube .cube__face {
    background: #fafafa;
    overflow: hidden; /* Aggiunto per prevenire sconfinamenti */
    border: 2px solid rgba(200, 200, 200, 0.6);
    backface-visibility: hidden; /* Nasconde le facce posteriori */
    transform-style: preserve-3d; /* Mantiene la struttura 3D */
}

.color-cube .cube__face.black {
    background: #000;
    color: white;
    border: 2px solid rgba(100, 100, 100, 0.6);
}

/* Assicuriamo che anche il testo nei cubi sia ben centrato */
.cube__face {
    display: flex;
    align-items: center;
    justify-content: center;
    /* overflow: hidden; */ /* Commentato per testare la risoluzione del problema di clipping agli angoli */
    font-size: 60px; /* Dimensione ridotta per evitare problemi negli angoli */
}

/* Stili per i dadi 3D animati */
.dice-container {
    display: flex;
    justify-content: center; /* Centrato orizzontalmente */
    width: 100%;
    max-width: 750px; /* Aumentato da 600px a 750px */
}

.scene {
    width: 200px;
    height: 200px;
    perspective: 600px;
    margin: 20px;
}

.cube {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transform: translateZ(-100px);
    /* Rimuoviamo questa transizione che potrebbe confliggere */
    /* transition: transform 1s; */
}

.cube.number-cube {
    /* Aumentata durata e cambiata timing function. RIMOSSO 'forwards'. */
    animation: spinNumberCube 3.5s cubic-bezier(0.3, 0.7, 0.1, 1);
    animation-play-state: paused;
}

.cube.letter-cube {
    /* Aumentata durata e cambiata timing function. RIMOSSO 'forwards'. */
    animation: spinLetterCube 3.7s cubic-bezier(0.3, 0.7, 0.1, 1);
    animation-delay: 0.1s; /* Leggero ritardo */
    animation-play-state: paused;
}

.cube.color-cube {
    /* Aumentata durata e cambiata timing function. RIMOSSO 'forwards'. */
    animation: spinColorCube 3.9s cubic-bezier(0.3, 0.7, 0.1, 1);
    animation-delay: 0.2s; /* Leggero ritardo */
    animation-play-state: paused;
}

/* Keyframes più complessi per rotazioni più lunghe e varie */
@keyframes spinNumberCube {
    0%   { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    15%  { transform: translateZ(-100px) rotateX(180deg) rotateY(90deg) rotateZ(45deg); }
    30%  { transform: translateZ(-100px) rotateX(-90deg) rotateY(180deg) rotateZ(-30deg); }
    45%  { transform: translateZ(-100px) rotateX(270deg) rotateY(270deg) rotateZ(90deg); }
    60%  { transform: translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(180deg); }
    75%  { transform: translateZ(-100px) rotateX(450deg) rotateY(450deg) rotateZ(270deg); }
    90%  { transform: translateZ(-100px) rotateX(540deg) rotateY(540deg) rotateZ(360deg); }
    100% { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); } /* Forza fine a rotazione zero */
}

@keyframes spinLetterCube {
    0%   { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    15%  { transform: translateZ(-100px) rotateX(-90deg) rotateY(180deg) rotateZ(60deg); }
    30%  { transform: translateZ(-100px) rotateX(180deg) rotateY(270deg) rotateZ(-45deg); }
    45%  { transform: translateZ(-100px) rotateX(90deg) rotateY(360deg) rotateZ(120deg); }
    60%  { transform: translateZ(-100px) rotateX(360deg) rotateY(450deg) rotateZ(240deg); }
    75%  { transform: translateZ(-100px) rotateX(450deg) rotateY(540deg) rotateZ(300deg); }
    90%  { transform: translateZ(-100px) rotateX(540deg) rotateY(630deg) rotateZ(360deg); }
    100% { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); } /* Forza fine a rotazione zero */
}

@keyframes spinColorCube {
    0%   { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    15%  { transform: translateZ(-100px) rotateX(90deg) rotateY(-90deg) rotateZ(30deg); }
    30%  { transform: translateZ(-100px) rotateX(180deg) rotateY(180deg) rotateZ(-60deg); }
    45%  { transform: translateZ(-100px) rotateX(270deg) rotateY(90deg) rotateZ(150deg); }
    60%  { transform: translateZ(-100px) rotateX(360deg) rotateY(360deg) rotateZ(210deg); }
    75%  { transform: translateZ(-100px) rotateX(450deg) rotateY(450deg) rotateZ(270deg); }
    90%  { transform: translateZ(-100px) rotateX(540deg) rotateY(540deg) rotateZ(360deg); }
    100% { transform: translateZ(-100px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); } /* Forza fine a rotazione zero */
}

.cube__face {
    position: absolute;
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 80px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
    border-radius: 20px; /* Aumentato a 20px per rendere gli angoli meno appuntiti */
    overflow: hidden; /* Aggiunto per prevenire sconfinamenti */
    backface-visibility: visible; /* Tentativo di correzione clipping */
}

.number-cube .cube__face {
    background: #e53935;
    color: white;
    font-weight: bold;
}

.letter-cube .cube__face {
    background: #3f51b5;
    color: white;
    font-weight: bold;
}

.color-cube .cube__face {
    background: #fafafa;
}

.color-cube .cube__face.black {
    background: #000;
    color: white;
}

.cube__face--front  { transform: rotateY(  0deg) translateZ(100px); }
.cube__face--right  { transform: rotateY( 90deg) translateZ(100px); }
.cube__face--back   { transform: rotateY(180deg) translateZ(100px); }
.cube__face--left   { transform: rotateY(-90deg) translateZ(100px); }
.cube__face--top    { transform: rotateX( 90deg) translateZ(100px); }
.cube__face--bottom { transform: rotateX(-90deg) translateZ(100px); }

.control-panel {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 15px;
}

.control-panel button {
    padding: 10px 20px;
    border: none;
    border-radius: 50px;
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    min-width: 180px;
}

.control-panel button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(145deg, #4aa3df, #3498db);
}

.control-panel button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dice-label {
    font-size: 14px; /* Leggermente ridotto da valore precedente */
    font-weight: bold;
    color: #555;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.column {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px; /* Aggiunto margine orizzontale */
}

/* Rimuovo le vecchie definizioni g1 e g2 che sono ora sostituite da quelle migliorate sopra */
/*
.g1 {
    font-size: 40px;
    color: #000000 !important;
    text-shadow: none !important;
    -webkit-text-stroke: 0.5px #000000 !important;
    font-weight: 900 !important;
}

.g2 {
    font-size: 40px;
}
*/

/* Fine stili dadi 3D */


/* --- Classi CSS per Stato Finale Dadi --- */

/* Stato finale per Dado Numerico */
.cube.show-num-1 { transform: translateZ(-100px) rotateY(0deg); }
.cube.show-num-2 { transform: translateZ(-100px) rotateY(180deg); }
.cube.show-num-3 { transform: translateZ(-100px) rotateY(-90deg); }
.cube.show-num-4 { transform: translateZ(-100px) rotateY(90deg); }
.cube.show-num-5 { transform: translateZ(-100px) rotateX(-90deg); }
.cube.show-num-6 { transform: translateZ(-100px) rotateX(90deg); }

/* Stato finale per Dado Alfabetico */
.cube.show-let-A { transform: translateZ(-100px) rotateY(0deg); }
.cube.show-let-B { transform: translateZ(-100px) rotateY(180deg); }
.cube.show-let-C { transform: translateZ(-100px) rotateY(-90deg); }
.cube.show-let-D { transform: translateZ(-100px) rotateY(90deg); }
.cube.show-let-E { transform: translateZ(-100px) rotateX(-90deg); }
.cube.show-let-F { transform: translateZ(-100px) rotateX(90deg); }

/* Stato finale per Dado Colore */
.cube.show-col-white { transform: translateZ(-100px) rotateY(0deg); }
.cube.show-col-black { transform: translateZ(-100px) rotateY(180deg); }

/* Media queries per adattarsi a diversi livelli di zoom e dimensioni dello schermo */
@media screen and (max-width: 1600px) {
    #game-container {
        grid-template-columns: minmax(510px, 33%) 1fr;
        gap: 0.6vw;
    }

    #board-area {
        grid-template-columns: 1fr min(32%, 450px);
        grid-template-rows: auto 1fr;
    }

    #game-board {
        /* Mantenuto il rapporto altezza/larghezza per il formato carta da gioco */
        --cell-width: min(4.1vw, 23vh, 120px); /* Ridotto di 10px per notebook e laptop */
        --cell-height: calc(var(--cell-width) * 1.46);
        padding: min(2vw, 32px); /* Aumentato per dare più spazio alle notazioni */
    }

    /* Rimossa regola specifica per livelli di zoom inferiori al 100% */

    /* Le carte mantengono dimensioni fisse */
    .hand-area {
        padding: 6px;
        gap: 4px;
        max-height: 100%; /* Assicura che non superi mai il contenitore */
    }

    /* Assicuriamo che gli slot delle carte mantengano il gradiente */
    .card-slot {
        background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9));
        box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3);
        border: 1px solid rgba(100, 150, 255, 0.3);
    }

    .card-slot:hover {
        background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9));
        border: 1px solid rgba(120, 180, 255, 0.4);
    }
}

@media screen and (max-width: 1200px) {
    #game-container {
        grid-template-columns: minmax(500px, 32%) 1fr;
        gap: 0.5vw;
        padding: 0.5vw;
    }

    #board-area {
        grid-template-columns: 1fr min(30%, 420px);
        gap: 8px;
        padding: 8px;
    }

    #game-board {
        /* Mantenuto il rapporto altezza/larghezza per il formato carta da gioco */
        --cell-width: min(3.6vw, 21vh, 110px); /* Ridotto di 10px per notebook e laptop */
        --cell-height: calc(var(--cell-width) * 1.46);
        padding: min(1.8vw, 28px); /* Aumentato per dare più spazio alle notazioni */
        gap: min(0.25vw, 3px); /* Ridotto leggermente */
    }

    /* Le carte mantengono dimensioni fisse */
    .hand-area {
        padding: 5px;
        gap: 3px;
        max-height: 100%; /* Assicura che non superi mai il contenitore */
    }

    /* Assicuriamo che gli slot delle carte mantengano il gradiente */
    .card-slot {
        background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9));
        box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3);
        border: 1px solid rgba(100, 150, 255, 0.3);
    }

    .card-slot:hover {
        background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9));
        border: 1px solid rgba(120, 180, 255, 0.4);
    }
}

@media screen and (max-width: 992px) {
    #game-container {
        grid-template-columns: minmax(490px, 30%) 1fr;
        gap: 0.4vw;
        padding: 0.4vw;
    }

    #board-area {
        grid-template-columns: 25px 1fr min(28%, 380px);
        gap: 6px;
        padding: 6px;
    }

    #advantage-indicator {
        width: 25px;
        border-radius: 0; /* Rimosso border-radius */
    }

    #game-board {
        /* Mantenuto il rapporto altezza/larghezza per il formato carta da gioco */
        --cell-width: min(3.1vw, 19vh, 110px); /* Ridotto ulteriormente */
        --cell-height: calc(var(--cell-width) * 1.46);
        padding: min(1.5vw, 24px); /* Aumentato per dare più spazio alle notazioni */
        gap: min(0.2vw, 2px); /* Ridotto leggermente */
        border: min(0.3vw, 5px) solid rgba(10, 20, 40, 0.9); /* Ridotto leggermente */
    }

    /* Le carte mantengono dimensioni fisse */
    .hand-area {
        padding: 4px;
        gap: 2px;
        max-height: 100%; /* Assicura che non superi mai il contenitore */
    }

    /* Assicuriamo che gli slot delle carte mantengano il gradiente */
    .card-slot {
        background: radial-gradient(circle at center, rgba(40, 80, 140, 0.7), rgba(20, 40, 80, 0.9));
        box-shadow: 0 3px 8px rgba(0,0,0,0.3), 0 0 12px rgba(0, 100, 255, 0.2), inset 0 0 15px rgba(60, 120, 200, 0.3);
        border: 1px solid rgba(100, 150, 255, 0.3);
    }

    .card-slot:hover {
        background: radial-gradient(circle at center, rgba(50, 100, 180, 0.8), rgba(30, 60, 120, 0.9));
        border: 1px solid rgba(120, 180, 255, 0.4);
    }
}


/* Classi per visibilità facce dado (tentativo correzione conflitti) */
.face-visible {
    opacity: 1 !important;
    visibility: visible !important; /* Aggiungo anche visibility per maggiore robustezza */
}

.face-hidden {
    opacity: 0 !important;
    visibility: hidden !important; /* Aggiungo anche visibility */
}

/* Stili per la faccia 2D finale (sostituzione del cubo 3D) */
.final-dice-face {
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px; /* Ridotto ulteriormente a 8px */
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    position: relative;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    font-size: 80px; /* Dimensione base per numeri/lettere */
    transition: all 0.5s ease-out; /* Transizione per tutti gli effetti */
    opacity: 1; /* Assicuriamo sia visibile di default una volta aggiunta */
    z-index: 10; /* Assicura che sia sopra alle altre facce */
    transform: translateZ(0); /* Miglior rendering */
    animation: final-face-appear 0.6s ease-out forwards; /* Animazione di apparizione */
}

/* Regola unificata per g1 e g2 in tutti gli stati */
.g1, .g2,
.dice-animating .g1,
.dice-animating .g2,
.dice-rolling .g1,
.dice-rolling .g2,
.cube.spinning .g1,
.cube.spinning .g2,
.cube__face .g1,
.cube__face .g2 {
    font-size: 70px !important; /* Dimensione fissa per tutti gli stati */
    transform: scale(1.25) !important; /* Scala fissa per tutti gli stati */
    transition: none !important; /* Rimuoviamo tutte le transizioni */
    animation: none !important; /* Rimuoviamo tutte le animazioni */
}

@keyframes final-face-appear {
    0% {
        transform: scale(0.9) translateZ(0);
        opacity: 0.7;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    70% {
        transform: scale(1.05) translateZ(0);
        opacity: 1;
        box-shadow: 0 15px 30px rgba(0,0,0,0.4);
    }
    100% {
        transform: scale(1) translateZ(0);
        opacity: 1;
        box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    }
}

.final-face-color-white {
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    border: 3px solid rgba(210, 210, 210, 0.8);
}

.final-face-color-black {
    background: linear-gradient(135deg, #333333, #000000);
    border: 3px solid rgba(80, 80, 80, 0.8);
}

/* Stili specifici per g1/g2 all'interno della faccia 2D finale */
.final-dice-face .g1,
.final-dice-face .g2 {
    font-size: 135px !important; /* Dimensione fissa per la faccia finale */
    font-weight: 900;
    font-family: 'Arial', sans-serif;
    position: relative;
    display: inline-block; /* Permette di avere una dimensione propria */
    width: auto; /* Dimensione automatica basata sul contenuto */
    height: auto;
    line-height: 1;
    text-align: center;
    transform: scale(1.25) translateZ(0) !important; /* Scala fissa */
    letter-spacing: 0; /* Riduce lo spazio tra i caratteri */
    transition: none !important; /* Rimuoviamo tutte le transizioni */
    animation: none !important; /* Rimuoviamo tutte le animazioni */
}

.final-dice-face .g1 {
    color: #000000 !important;
    text-shadow: none !important;
    /* Rimuoviamo l'animazione pulsante */
}

.final-dice-face .g2 {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.6) !important;
    /* Rimuoviamo l'animazione pulsante */
}

@keyframes pulse-g1 {
    0% { transform: scale(1.25); }
    50% { transform: scale(1.4); opacity: 0.95; }
    100% { transform: scale(1.25); }
}

@keyframes pulse-g2 {
    0% { transform: scale(1.25); text-shadow: 0 2px 4px rgba(0,0,0,0.6); }
    50% { transform: scale(1.4); text-shadow: 0 3px 6px rgba(0,0,0,0.8); }
    100% { transform: scale(1.25); text-shadow: 0 2px 4px rgba(0,0,0,0.6); }
}

/* Aggiungiamo un effetto di brillantezza sulla faccia finale */
.final-dice-face::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px; /* Ridotto ulteriormente a 8px */
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%);
    pointer-events: none;
    z-index: 1;
}

.final-face-number {
    background: #e53935;
    color: white;
}

.final-face-letter {
    background: #3f51b5;
    color: white;
}

/* Layout principale per schermi grandi e 2K */
@media screen and (min-width: 1800px) {
    #game-container {
        grid-template-columns: min-content minmax(900px, 35%) 1fr;
        gap: 0;
        padding: 0;
        padding-right: 0.8vw;
        padding-bottom: 0.8vw;
        height: 100vh;
        max-height: 100vh;
    }

    #board-area {
        grid-template-columns: 35px 1fr min(35%, 480px);
        grid-template-rows: auto 1fr;
        height: calc(100% - 15px); /* Manteniamo la riduzione dell'altezza */
        margin-top: 15px; /* Manteniamo il margine superiore */
    }

    #advantage-indicator {
        width: 35px;
        border-radius: 6px; /* Ridotto da 17px a 6px per renderlo meno arrotondato */
    }

    #players-column {
        padding: 12px;

    }

    #player1-area, #player2-area {
        padding: 15px;
        border-radius: 15px;
    }

    /* Le aree delle mani dei giocatori avranno più spazio verticale */
    #player1-hand, #player2-hand {
        max-height: 100%; /* Assicura che non superi mai il contenitore */
    }

    .game-sidebar {
        width: 70px; /* Leggermente più largo su schermi grandi */
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .game-sidebar .logo {
        padding: 30px 0 15px 0;
    }

    /* Rimosso l'espansione al passaggio del mouse per schermi grandi */

    .game-sidebar .logo .logo-image {
        width: 45px;
    }

    .game-sidebar li i {
        font-size: 22px;
    }
}

/* Layout specifico per 2K (2560x1440) e superiori */
@media screen and (min-width: 2000px) {
    #game-container {
        grid-template-columns: min-content minmax(580px, 35%) 1fr;
        gap: 0;
        padding: 0;
        height: 100vh;
        max-height: 100vh;
    }

    #board-area {
        grid-template-columns: 40px 1fr min(38%, 520px);
        height: calc(100% - 10px); /* Manteniamo la riduzione dell'altezza */
        margin-top: 5px; /* Manteniamo il margine superiore */
    }

    #advantage-indicator {
        width: 40px;
        border-radius: 0; /* Rimosso border-radius */
    }

    #players-column {
        padding: 15px;
    }

    #player1-area, #player2-area {
        padding: 18px;
        border-radius: 18px;
        margin-bottom: 20px;
    }

    /* Le aree delle mani dei giocatori avranno ancora più spazio verticale */
    #player1-hand, #player2-hand {
        max-height: 100%; /* Assicura che non superi mai il contenitore */
    }

    .game-sidebar {
        width: 80px; /* Ancora più largo su schermi molto grandi */
        height: 100vh;
        margin: 0;
        border-radius: 0;
    }

    .game-sidebar .logo {
        padding: 30px 0 15px 0;
    }

    /* Rimosso l'espansione al passaggio del mouse per schermi molto grandi */

    .game-sidebar .logo .logo-image {
        width: 50px;
    }

    .game-sidebar li i {
        font-size: 24px;
        width: 80px;
    }

    /* Rimosso lo stile degli elementi li al passaggio del mouse sulla sidebar per schermi molto grandi */
}

/* Miglioramenti per la visibilità su monitor ad alta risoluzione */
@media screen and (min-width: 2000px) {
    /* Aumenta la dimensione del testo nelle informazioni dei giocatori */
    .player-info {
        font-size: 1.2em;
    }

    /* Aumenta le dimensioni di elementi di gioco come bottoni e contatori */
    #game-info {
        font-size: 1.15em;
    }

    #pass-turn-button {
        font-size: 1.15em;
        padding: 12px 16px;
    }

    /* Migliora la visibilità del messaggio di gioco */
    #game-message {
        font-size: 21px; /* Modificato da 1.2em */
        padding: 12px;
    }

    /* Aumenta le dimensioni degli elementi del deck */
    .card-stack-visual {
        width: 130px;
        height: 190px;
    }

    .card-count {
        font-size: 1.1em;

    }
}

/* --- Stili per Loop Card (Visualizzazione CSS) --- */

/* Imposta la cella come contenitore relativo per lo pseudo-elemento */
.cell.has-loop {
    position: relative;
    overflow: visible; /* Assicura che lo pseudo-elemento non venga tagliato */
}

/* Stili per la carta loop */
.loop-card {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 10;
    pointer-events: none;
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
}

/* Rimosso il pseudo-elemento che aggiungeva il riquadro nero */

@keyframes pulseLoop {
    0% { opacity: 0.8; transform: scale(0.98) translateZ(0); }
    100% { opacity: 1; transform: scale(1.02) translateZ(0); }
}

/* Animazione per l'entrata fluida della carta loop */
@keyframes loopCardEnter {
    0% { opacity: 0; transform: scale(0.9) translateZ(0); filter: blur(2px); }
    50% { opacity: 0.7; transform: scale(1.05) translateZ(0); filter: blur(1px); }
    100% { opacity: 1; transform: scale(1) translateZ(0); filter: blur(0); }
}

/* Animazione per l'apparizione fluida delle carte loop */
@keyframes loopCardAppear {
    0% {
        opacity: 0;
        transform: scale(0.95);
        filter: blur(2px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
    }
}

/* Applicazione dell'animazione alle carte loop quando appaiono */
.card.loop {
    animation: loopCardAppear 0.5s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
    will-change: transform, opacity;
}

/* Contenitore per le carte loop per prevenire spostamenti del layout */
.loop-container {
    min-height: 0;
    transition: min-height 0.3s ease-out;
    position: relative;
}



/* Pseudo-elemento per mostrare l'immagine del loop */
.cell.has-loop::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('img/carte/Loop_card.webp'); /* Percorso principale */
    background-size: 100% 100%; /* Adatta esattamente alla cella */
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px; /* Stesso arrotondamento delle celle/carte */
    z-index: 15; /* Sopra la carta normale, sotto eventuali hover */
    pointer-events: none; /* Non interferisce con i click sulla cella */
    opacity: 0; /* Inizia con opacità 0 */
    box-sizing: border-box; /* Assicura che width/height includano padding/border */
    animation: loopCardEnter 0.5s cubic-bezier(0.2, 0.8, 0.2, 1) forwards, pulseLoop 2s infinite alternate 0.5s; /* Aggiungi animazione di entrata e poi pulsazione */
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.7); /* Aggiungi un'ombra dorata */
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Fallback per il percorso dell'immagine del loop */
@supports not (background-image: url('img/carte/Loop_card.webp')) {
    .cell.has-loop::after {
        background-image: url('img/carte/Loop_card.png'); /* Percorso alternativo con PNG */
    }
}

/* Fallback aggiuntivo per browser che non supportano WebP */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .cell.has-loop::after {
        background-image: url('img/carte/Loop_card.png'); /* Fallback per IE */
    }
}

/* Opzionale: Stili specifici per tipi di loop (es. filtro colore) */
/*
.cell.loop-symbolic::after {
    filter: hue-rotate(180deg) brightness(1.1);
}
.cell.loop-hybrid::after {
    filter: hue-rotate(270deg) brightness(1.1);
}
*/

/* Assicura che l'immagine del loop sia sopra la carta normale ma sotto l'hover della cella */
.cell .card.board-card {
    z-index: 20;
}
.cell:hover {
    z-index: 25; /* Assicura che l'hover sia sopra l'immagine del loop */
}
.cell.has-loop:hover::after {
     filter: brightness(1.2);
}

/* ===== SISTEMA DI RATING ===== */
.player-rating-container {
    margin-top: 10px;
    width: 100%;
}

/* ===== MESSAGGI DI ERRORE DI CONNESSIONE ===== */
#connection-error {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    max-width: 80%;
    animation: fadeInOut 0.5s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -20px); }
    100% { opacity: 1; transform: translate(-50%, 0); }
}

.player-rating {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.rating-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 2px solid gold;
    background-color: #0f2a40;
}

.level-info {
    flex-grow: 1;
}

.level-name {
    font-weight: bold;
    font-size: 0.9em;
    color: #ffcc00;
}

.level-rating {
    font-size: 1.1em;
    font-weight: bold;
    color: white;
}

.level-progress-container {
    height: 6px;
    background-color: #444;
    border-radius: 3px;
    margin-top: 5px;
    position: relative;
}

.level-progress {
    height: 100%;
    background-color: #4caf50;
    border-radius: 3px;
    transition: width 0.5s ease-in-out;
}

.level-range {
    position: absolute;
    top: -18px;
    right: 0;
    font-size: 0.7em;
    color: #aaa;
}

/* ===== NOTIFICA PROMOZIONE ===== */
.level-up-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background-color: rgba(20, 50, 80, 0.95);
    border: 3px solid gold;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
    z-index: 1000;
    max-width: 400px;
    text-align: center;
    opacity: 0;
    transition: all 0.5s ease-in-out;
}

.level-up-notification.visible {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.notification-header h3 {
    color: gold;
    font-size: 1.8em;
    margin: 0 0 15px 0;
}

.level-change {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
}

.old-level, .new-level {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 40%;
}

.old-level img, .new-level img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid #fff;
    margin-bottom: 5px;
}

.level-arrow {
    font-size: 2em;
    color: gold;
    margin-top: -10px;
}

.rating-change {
    font-size: 1.2em;
    color: #4caf50;
    margin-top: 10px;
}

/* ===== CLASSIFICA SCREEN ===== */
.classifica-container {
    max-width: 900px;
    width: 90%;
    margin: 20px auto;
    background-color: rgba(20, 40, 80, 0.9);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.classifica-header {
    text-align: center;
    margin-bottom: 20px;
}

.classifica-header h2 {
    color: gold;
    font-size: 2.2em;
    margin: 0 0 8px 0;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
}

.classifica-header p {
    color: #aaa;
    font-size: 1.1em;
    margin: 0;
}

.classifica-table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

.classifica-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    color: white;
}

.classifica-table th {
    background-color: rgba(0, 0, 0, 0.4);
    padding: 12px;
    font-weight: bold;
    color: #ffcc00;
    border-bottom: 2px solid gold;
}

.classifica-table tr {
    transition: background-color 0.3s;
}

.classifica-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.05);
}

.classifica-table tr:hover {
    background-color: rgba(255, 215, 0, 0.1);
}

.classifica-table td {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.position-col {
    width: 50px;
    text-align: center;
}

.avatar-col {
    width: 70px;
    text-align: center;
}

.player-col {
    width: 30%;
}

.level-col {
    width: 25%;
}

.rating-col {
    width: 15%;
    text-align: center;
}

.classifica-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid gold;
    background-color: #0f2a40;
}

.classifica-rating {
    font-weight: bold;
    color: #4caf50;
}

.classifica-level {
    display: flex;
    align-items: center;
    gap: 10px;
}

.classifica-legend {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.classifica-legend h3 {
    margin: 0 0 15px 0;
    color: #ffcc00;
    font-size: 1.4em;
}

.level-description ul {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    padding: 0;
    margin: 15px 0 0 0;
    list-style: none;
}

.level-badge {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    margin-right: 10px;
    font-weight: bold;
    font-size: 0.9em;
}

.level-badge.principiante { background-color: #b0bec5; color: #263238; }
.level-badge.dilettante-d { background-color: #81c784; color: #1b5e20; }
.level-badge.dilettante-c { background-color: #4fc3f7; color: #01579b; }
.level-badge.dilettante-b { background-color: #7986cb; color: #1a237e; }
.level-badge.dilettante-a { background-color: #9575cd; color: #311b92; }
.level-badge.candidato { background-color: #ffb74d; color: #e65100; }
.level-badge.maestro { background-color: #ff8a65; color: #bf360c; }
.level-badge.internazionale { background-color: #f06292; color: #880e4f; }
.level-badge.gran-maestro { background-color: #ba68c8; color: #4a148c; }
.level-badge.super { background-color: #ffd700; color: #7f6000; }

.top-position {
    font-weight: bold;
}

.position-1 { color: gold; }
.position-2 { color: silver; }
.position-3 { color: #cd7f32; }

.classifica-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.classifica-buttons button {
    min-width: 120px;
}

/* Stile specifico per il pulsante "Pesca Carta" quando è dentro #left-panel */
#left-panel > #pass-turn-button {
    margin-top: 10px; /* Aggiungi un po' di spazio sopra il pulsante */
    margin-left: 20px; /* Allinea il pulsante con gli altri elementi */
}

.card-count {
    font-size: 2.8em; /* Molto più grande anche nei media query */

}

/* Sovrascrivi dimensione font per contatore carte in ogni situazione */
.card-count {
    font-size: 2.5em !important; /* Sovrascrivi qualsiasi altra regola di font-size */
}

/* Stile per le carte multiple del mazzo */
.card-stack-visual {
    position: absolute; /* Mantenuto assoluto rispetto a #deck */
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: 12px;
    /* Rimosso transform-style e perspective per eliminare effetti 3D */
    --stack-layers: 5;
    --stack-height: 85%;
    z-index: 1; /* Assicura che non si sovrapponga ad altri elementi */
}

/* Generiamo multiple carte nel mazzo usando pseudo-elementi e variabili CSS */
.card-stack-visual::before,
.card-stack-visual .card-pile-1,
.card-stack-visual .card-pile-2,
.card-stack-visual .card-pile-3,
.card-stack-visual .card-pile-4,
.card-stack-visual .card-pile-5 {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background: url('img/carte/card-back.webp');
    background-size: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

/* Carta principale (superiore) */
.card-stack-visual::before {
    z-index: 10;
    /* Nessuna trasformazione o animazione */
    box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    cursor: pointer; /* Indica che il mazzo è cliccabile */
}

/* Aggiungiamo gli elementi per le carte impilate */
.card-stack-visual::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Aggiungiamo dinamicamente gli elementi per le carte nella pila */
#deck:not(.empty-deck)::before {
    content: '';
}

.card-stack-visual .card-pile-1,
.card-stack-visual .card-pile-2,
.card-stack-visual .card-pile-3,
.card-stack-visual .card-pile-4,
.card-stack-visual .card-pile-5 {
    /* Rimosse tutte le trasformazioni, messe in un unico selettore */
    position: absolute;
    opacity: 0.9;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.35);
}

/* Aggiungiamo elementi al div del mazzo */
#deck::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

/* Animazione della carta superiore che scorre verso l'alto */
@keyframes drawCardHint {
    0% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    }
    40% {
        transform: translateY(-20px) rotateX(-8deg);
        box-shadow: 0 25px 35px rgba(0,0,0,0.5), 0 0 40px rgba(0, 150, 255, 0.6);
        filter: brightness(1.15);
    }
    45% {
        transform: translateY(-20px) rotateX(-8deg);
        box-shadow: 0 25px 35px rgba(0,0,0,0.5), 0 0 40px rgba(0, 150, 255, 0.6);
        filter: brightness(1.15);
    }
    85% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
        filter: brightness(1);
    }
    100% {
        transform: translateY(0) rotateX(0deg);
        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(0, 100, 255, 0.3);
    }
}

/* Aggiungi un indicatore visivo al mazzo quando viene cliccato */
#deck:active .card-stack-visual::before {
    transform: translateY(-10px) scale(0.97);
    filter: brightness(0.9);
    animation-play-state: paused;
}

/* Variazioni di altezza per il mazzo in base al numero di carte */
#deck[data-cards="few"] .card-pile-4,
#deck[data-cards="few"] .card-pile-5 {
    display: none;
}

#deck[data-cards="medium"] .card-pile-5 {
    display: none;
}

/* Rimossi effetti hover sul mazzo */

#deck:hover .card-pile-5 {
    transform: translateZ(-15px) translateX(-22px) translateY(-22px) rotateZ(10deg);
}

#deck:hover .card-count {
    color: #e0f0ff;
    text-shadow: 0 0 15px rgba(100, 150, 255, 0.9);
}

/* Stili per i cerchietti colorati dei giocatori */
.player-color-circle {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.player-color-circle.white {
    background-color: #ffffff;
}

.player-color-circle.black {
    background-color: #000000;
}

/* Stile per il pallino colorato nell'intestazione dei giocatori */
.player-dot {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.player-dot.white {
    background-color: #ffffff;
}

.player-dot.black {
    background-color: #000000;
}

.player-header h2 {
    display: flex;
    align-items: center;
}

/* Stile per il wrapper dell'avatar del giocatore nell'intestazione */
.player-avatar-wrapper {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 3px; /* Spazio interno */
    transition: all 0.2s ease;
    min-width: 70px; /* Larghezza minima per assicurare spazio per il testo */
}

.player-avatar-wrapper:hover {
    transform: scale(1.03); /* Leggero effetto di ingrandimento */
}

/* Stile per il contenitore dell'avatar del giocatore nell'intestazione */
.player-avatar-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 48px; /* Aumentato da 32px */
    width: 60px; /* Aumentato da 40px */
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.7);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.player-avatar-container img {
    max-height: 95%; /* Aumentato da 90% */
    max-width: 95%; /* Aumentato da 90% */
    object-fit: contain;
    border-radius: 3px; /* Aggiunto per un aspetto migliore */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* Aggiunto per dare profondità */
    transition: transform 0.2s ease; /* Aggiunto per effetto hover */
}

.player-avatar-container img:hover {
    transform: scale(1.05); /* Leggero effetto di ingrandimento al passaggio del mouse */
}

/* Stile per il nome del livello dell'avatar */
.player-avatar-level {
    font-size: 0.75em; /* Aumentato per migliore leggibilità */
    color: #ffcc00;
    margin-top: 3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    width: 100%; /* Usa tutta la larghezza disponibile */
    text-align: center; /* Centra il testo */
    font-weight: bold;
    padding: 2px 0; /* Aggiunto padding verticale */
    letter-spacing: 0.5px; /* Migliora la leggibilità */
}

/* Stile per il box che contiene nome e avatar */
.player-info-box {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(30, 60, 120, 0.4), rgba(10, 20, 40, 0.6));
    border-radius: 8px;
    padding: 5px 10px;
    margin-left: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.2);
    transition: all 0.3s ease;
}

.player-info-box:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 15px rgba(100, 150, 255, 0.3);
    border: 1px solid rgba(100, 150, 255, 0.4);
    transform: translateY(-2px);
}

/* Animazione per il bagliore del nome del giocatore */
/* playerNameGlow animation removed to prevent flickering during card distribution */

/* Animazione per il bagliore del giocatore attivo */
/* activePlayerGlow animation removed to prevent flickering during card distribution */

.player-name {
    display: inline-block;
    vertical-align: middle;
    font-weight: bold;
    font-size: 1.1em;
    margin-right: 3px;
    color: #ffffff;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3), 0 0 10px rgba(100, 150, 255, 0.2);
    letter-spacing: 0.5px;
    /* Hardware acceleration to prevent flickering during animations */
    transform: translateZ(0);
    backface-visibility: hidden;
    /* Remove focus outline */
    outline: none;
}

.player-name:focus {
    outline: none;
}

/* Remove focus outline from all player-related elements */
.player-header h2,
.player-header h2:focus,
.player-info-box,
.player-info-box:focus {
    outline: none !important;
}

/* Remove focus outline from all clickable elements in player area */
#player1-area *:focus,
#player2-area *:focus,
#player1-area *,
#player2-area * {
    outline: none !important;
}

/* Ensure no focus rings on any text elements */
span:focus,
h2:focus,
div:focus {
    outline: none !important;
}

/* Override any browser default focus styles for these specific elements */
#player1-area .player-name,
#player2-area .player-name,
#player1-area .player-info-box,
#player2-area .player-info-box {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.player-rating {
    display: inline-block;
    vertical-align: middle;
    font-size: 0.9em;
    color: #ffd700;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    margin-right: 10px;
}

/* Stile per il nome del giocatore attivo */
.player-name.active {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(255, 215, 0, 0.7), 0 0 30px rgba(255, 215, 0, 0.4);
    color: #ffdd00;
    font-weight: bold;
}

.names-container h2 {
    color: #2c3e50;
    font-size: 1.8em;
    margin: 0 0 10px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Timer integrato (totale + clessidra) */
.total-timer-integrated {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(135deg, rgba(30, 60, 120, 0.6), rgba(10, 20, 40, 0.8));
    padding: 8px 16px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 150, 255, 0.2);
    border: 1px solid rgba(100, 150, 255, 0.3);
    transition: all 0.3s ease;
    margin-left: 10px; /* Modificato per allinearlo meglio con il nuovo box */
    position: relative;
}

.total-timer-integrated.active {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 30px rgba(100, 150, 255, 0.6);
    border: 1px solid rgba(100, 150, 255, 0.6);
    transform: scale(1.05);
}

.timer-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.total-timer-integrated i {
    color: #f8cd38;
    margin-right: 8px;
    font-size: 1.2em;
    text-shadow: 0 0 10px rgba(248, 205, 56, 0.7);
}

.total-timer-integrated .total-timer-count {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #fff;
    font-size: 1.5em;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Timer con poco tempo rimasto */
.total-timer-integrated.warning .total-timer-count {
    color: #ffcc00;
    text-shadow: 0 0 10px rgba(255, 204, 0, 0.7);
    animation: pulseWarning 1s infinite alternate;
}

/* Timer quasi scaduto */
.total-timer-integrated.danger .total-timer-count {
    color: #ff3b30;
    text-shadow: 0 0 10px rgba(255, 59, 48, 0.7), 0 0 20px rgba(255, 59, 48, 0.5);
    animation: pulseDanger 0.5s infinite alternate;
}

/* Effetti sul contenitore del timer quando è in stato di avviso o pericolo */
.total-timer-integrated.warning .hourglass {
    filter: drop-shadow(0 0 8px rgba(255, 204, 0, 0.7));
}

.total-timer-integrated.danger .hourglass {
    filter: drop-shadow(0 0 12px rgba(255, 59, 48, 0.7));
    transform: scale(1.15);
}

/* Manteniamo il timer totale e la clessidra nello stato attivo/inattivo */
.total-timer-integrated.active .hourglass-top {
    animation-play-state: running;
}

.total-timer-integrated.active .hourglass-bottom {
    animation-play-state: running;
}

/* Elementi info rimossi per guadagnare spazio */

/* Authentication Styles for Sidebar */
.sidebar-footer {
    margin-top: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.sidebar-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;
}

.auth-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.auth-btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

#logout-button {
    background-color: #e74c3c;
}

#logout-button:hover {
    background-color: #c0392b;
}

.user-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

#username-display, #game-username-display {
    font-weight: 600;
    color: #3498db;
}

#user-rank, #game-user-rank {
    font-size: 0.8rem;
    color: #f1c40f;
}
/* OTTIMIZZAZIONE COMPLETA PER NOTEBOOK E DESKTOP */
@media screen and (min-width: 992px) {
    /* Layout principale del container di gioco */
    #game-container {
        display: grid \!important;
        grid-template-columns: auto 1fr \!important;
        gap: 0.8vw \!important;
        padding: 0.8vw \!important;
        height: 100vh \!important;
        width: 100% \!important;
        box-sizing: border-box \!important;
        overflow: hidden \!important;
    }

    /* Layout area principale (players + board) */
    #players-column {
        width: 100% \!important;
        display: flex \!important;
        flex-direction: column \!important;
        gap: 10px \!important;
        height: 100% \!important;
    }

    /* Layout board area */
    #board-area {
        display: grid \!important;
        grid-template-columns: 32px 1fr 350px \!important;
        gap: 10px \!important;
        height: 100% \!important;
        box-sizing: border-box \!important;
    }

    /* Stile per le aree dei giocatori */
    #player1-area, #player2-area {
        padding: 15px \!important;
        display: flex \!important;
        flex-direction: column \!important;
        justify-content: space-between \!important;
        align-items: center \!important;
        min-height: 200px \!important;
        flex: 1 \!important;
        box-sizing: border-box \!important;
        background-color: rgba(15, 25, 40, 0.8) \!important;
        border-radius: 10px \!important;
    }

    /* Ottimizzazione per intestazioni dei giocatori */
    .player-header {
        width: 100% \!important;
        display: flex \!important;
        justify-content: space-between \!important;
        align-items: center \!important;
        margin-bottom: 15px \!important;
    }

    /* Configurazione dell'area mano */
    .hand-area,
    #player1-hand,
    #player2-hand {
        display: grid \!important;
        grid-template-columns: repeat(5, 90px) \!important;
        column-gap: 30px \!important;
        row-gap: 10px \!important;
        padding: 15px \!important;
        width: 100% \!important;
        flex: 1 \!important;
        justify-content: center \!important;
        align-items: center \!important;
        position: relative \!important;
        margin: 0 \!important;
        box-sizing: border-box \!important;
    }

    /* Stile per le carte e gli slot */
    .hand-area .card-slot,
    #player1-hand .card-slot,
    #player2-hand .card-slot,
    .hand-area .card,
    #player1-hand .card,
    #player2-hand .card {
        width: 90px \!important;
        height: 131px \!important;
        border-radius: 5px \!important;
        max-width: 90px \!important;
        max-height: 131px \!important;
        min-width: 90px \!important;
        min-height: 131px \!important;
        margin: 0 \!important;
        box-sizing: border-box \!important;
        position: relative \!important;
    }

    /* Ottimizzazione per la barra laterale e altri elementi non modificata */

    /* Ottimizzazione per la barra laterale */
    #board-sidebar {
        width: 100% \!important;
        height: 100% \!important;
        display: flex \!important;
        flex-direction: column \!important;
    }
}

/* MIGLIORAMENTO AREE GIOCATORI E SPAZIO CARTE */
@media screen and (min-width: 992px) {
    /* Miglioramento delle aree giocatori */
    #player1-area {
        margin-bottom: 10px !important;
    }

    #player2-area {
        margin-top: 10px !important;
    }

    /* Ottimizzazione spazio per le carte */
    .hand-area {
        min-height: 150px !important;
        align-items: center !important;
        background: rgba(10, 20, 40, 0.5) !important;
        border-radius: 8px !important;
        box-shadow: inset 0 0 15px rgba(0, 20, 50, 0.5) !important;
        transition: none !important;
    }

    .hand-area:hover {
        /* Hover temporaneamente disabilitato per evitare flash */
    }

    /* Migliore organizzazione delle carte in mano */
    .hand-area .card-slot {
        transition: none !important;
    }

    /* Effetto hover migliorato e più controllato */
    .hand-area .card-slot:hover {
        /* Hover temporaneamente disabilitato per evitare flash */
    }

    /* Effetto carta selezionata più visibile */
    .hand-area .card-slot .card.selected {

        box-shadow: 0 10px 25px rgba(0,0,0,0.4), 0 0 30px rgba(50, 220, 100, 0.6) !important; /* Bagliore esistente */
        border: 2px solid rgba(50, 220, 100, 0.8) !important; /* Bordo esistente */
        z-index: 20 !important;
    }
}

/* STANDARDIZZAZIONE DIMENSIONI CARTE E SLOT */
@media screen and (min-width: 992px) {
    /* Dimensioni standardizzate per diverse risoluzioni desktop */
    :root {
        --card-width-desktop: 90px \!important;
        --card-height-desktop: calc(var(--card-width-desktop) * 1.46) \!important;
        --card-border-radius-desktop: 5px \!important;
    }

    /* Impostazioni universali per carte nel desktop */
    .card-slot,
    .hand-area .card-slot,
    #player1-hand .card-slot,
    #player2-hand .card-slot,
    .hand-area .card,
    #player1-hand .card,
    #player2-hand .card {
        width: var(--card-width-desktop) \!important;
        height: var(--card-height-desktop) \!important;
        border-radius: var(--card-border-radius-desktop) \!important;
        max-width: var(--card-width-desktop) \!important;
        max-height: var(--card-height-desktop) \!important;
        min-width: var(--card-width-desktop) \!important;
        min-height: var(--card-height-desktop) \!important;
        margin: 0 \!important;
        box-sizing: border-box \!important;
        position: relative \!important;
    }

    /* Schermi più grandi (1440p) */
    @media screen and (min-width: 2000px) {
        :root {
            --card-width-desktop: 100px \!important;
        }

        /* Adatta dimensioni grid */
        .hand-area,
        #player1-hand,
        #player2-hand {
            grid-template-columns: repeat(5, var(--card-width-desktop)) \!important;
        }
    }

    /* Schermi medi (1080p) */
    @media screen and (max-width: 1999px) and (min-width: 1200px) {
        :root {
            --card-width-desktop: 90px \!important;
        }

        /* Adatta dimensioni grid */
        .hand-area,
        #player1-hand,
        #player2-hand {
            grid-template-columns: repeat(5, var(--card-width-desktop)) \!important;
        }
    }

    /* Schermi più piccoli (laptop, notebook) */
    @media screen and (max-width: 1199px) and (min-width: 992px) {
        :root {
            --card-width-desktop: 85px \!important;
        }

        /* Adatta dimensioni grid */
        .hand-area,
        #player1-hand,
        #player2-hand {
            grid-template-columns: repeat(5, var(--card-width-desktop)) \!important;
            column-gap: 20px \!important;
        }
    }

    /* Assicuriamo che le carte nel tabellone abbiano dimensioni proporzionate */
    .cell .card-slot,
    .cell .card,
    .board-card {
        /* Dimensioni proporzionate all'area del tabellone */
        width: var(--cell-width) \!important;
        height: var(--cell-height) \!important;
        margin: 0 \!important;
        box-sizing: border-box \!important;
    }
}

/* Assicuriamo che le carte nel tabellone abbiano dimensioni proporzionate */
.cell .card-slot,
.cell .card,
.board-card {
    /* Dimensioni proporzionate all'area del tabellone */
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    min-width: 0 !important;
    min-height: 0 !important;
}

    /* Miglioramento celle e vertici */
    .cell {
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
    }

    .vertex {
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
    }

    /* Celle e vertici giocabili più evidenti */
    .cell.playable-cell {
        animation: cellPlayablePulse 2s infinite alternate !important;
    }

    .vertex.playable-vertex {
        animation: vertexPlayablePulse 2s infinite alternate !important;
    }

    /* Animazioni ottimizzate */
    @keyframes cellPlayablePulse {
        0% { box-shadow: 0 0 10px rgba(100, 200, 255, 0.5), inset 0 0 8px rgba(100, 200, 255, 0.4) !important; }
        100% { box-shadow: 0 0 20px rgba(100, 200, 255, 0.7), inset 0 0 15px rgba(100, 200, 255, 0.6) !important; }
    }

    @keyframes vertexPlayablePulse {
        0% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.6), inset 0 0 10px rgba(255, 215, 0, 0.5) !important; }
        100% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.8), inset 0 0 18px rgba(255, 215, 0, 0.7) !important; }
    }

/* MIGLIORAMENTO RESPONSIVITA' BARRA LATERALE */
@media screen and (min-width: 992px) {
    /* Ottimizzazione barra laterale del gioco */
    #game-sidebar {
        width: 60px \!important; /* Larghezza ottimizzata */
        background-color: rgba(10, 20, 35, 0.9) \!important;
        transition: width 0.3s ease-in-out \!important;
    }

    /* Espansione on hover */
    #game-sidebar:hover {
        width: 180px \!important;
    }

    /* Transizione per il testo */
    #game-sidebar li span {
        opacity: 0 \!important;
        transform: translateX(-20px) \!important;
        transition: all 0.3s ease-in-out \!important;
    }

    #game-sidebar:hover li span {
        opacity: 1 \!important;
        transform: translateX(0) \!important;
    }

    /* Stile per il profilo utente quando il menu è espanso */
    #game-sidebar:hover #game-user-profile span {
        opacity: 1 \!important;
        transform: translateX(0) \!important;
        display: inline-block \!important;
    }

    #game-sidebar:hover #game-user-profile .menu-tooltip {
        display: none \!important;
    }
}

    /* Miglioramento barra laterale del board */
    #board-sidebar {
        width: 100% \!important;
        max-width: 350px \!important;
        overflow: hidden \!important;
        background-color: rgba(15, 25, 40, 0.85) \!important;
        border-radius: 10px \!important;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4), inset 0 1px 1px rgba(255, 255, 255, 0.1) \!important;
    }

    /* Tab della barra laterale */
    #sidebar-tabs {
        display: flex \!important;
        background-color: rgba(5, 15, 30, 0.7) \!important;
        padding: 10px \!important;
        border-radius: 10px 10px 0 0 \!important;
    }

    .tab {
        padding: 8px 12px \!important;
        margin-right: 5px \!important;
        border-radius: 5px \!important;
        cursor: pointer \!important;
        transition: all 0.2s ease \!important;
    }

    .tab:hover {
        background-color: rgba(30, 60, 100, 0.6) \!important;
    }

    .tab.active {
        background-color: rgba(40, 80, 150, 0.7) \!important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) \!important;
    }

    /* Contenuto del tab */
    .tab-content {
        padding: 15px \!important;
        display: none \!important;
        overflow-y: auto \!important;
        max-height: 80vh \!important;
    }

    .tab-content.active {
        display: block \!important;
    }

    /* Stile bottoni azione */
    .action-button {
        padding: 10px 15px \!important;
        margin: 5px \!important;
        background: linear-gradient(to bottom, rgba(30, 60, 120, 0.9), rgba(20, 40, 80, 0.9)) \!important;
        border: 1px solid rgba(50, 100, 200, 0.4) \!important;
        border-radius: 5px \!important;
        color: #e0f0ff \!important;
        font-weight: 600 \!important;
        cursor: pointer \!important;
        transition: all 0.2s ease \!important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) \!important;
    }

    .action-button:hover {
        background: linear-gradient(to bottom, rgba(40, 80, 150, 0.9), rgba(30, 60, 120, 0.9)) \!important;
        transform: translateY(-2px) \!important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4) \!important;
    }

    .action-button:active {
        transform: translateY(1px) \!important;
        box-shadow: 0 1px 5px rgba(0, 0, 0, 0.5) \!important;
    }

/* INDICATORE DI VANTAGGIO - VERSIONE UNIFICATA */
@media screen and (min-width: 992px) {
    /* Layout board area */
    body #board-area {
        display: flex \!important;
        align-items: center \!important;
        gap: 4px \!important;
        height: 100% \!important;
        box-sizing: border-box \!important;
    }

    /* Soluzione diretta per l'indicatore di vantaggio */
    body #board-area #advantage-indicator {
        width: 25px \!important;
        height: 100% \!important; /* Altezza completa */
        margin: 0 \!important; /* Nessun margine */
        padding: 0 \!important; /* Rimuove tutti i padding */
        align-self: stretch \!important; /* Si estende per tutta l'altezza */
        border-radius: 0 \!important;
        overflow: hidden \!important;
        box-shadow: 0 0 10px rgba(0,0,0,0.3), inset 0 0 8px rgba(0,0,0,0.3) \!important;
        z-index: 5 \!important;
        position: relative \!important; /* Necessario per il posizionamento assoluto dei figli */
    }

    /* Divisore centrale */
    body #advantage-indicator::after {
        height: 1px \!important;
    }

    /* Sezioni bianca e nera - impostate esattamente alla metà dell'altezza totale */
    body #advantage-indicator .advantage-white {
        border-radius: 0 \!important;
        height: 50% \!important; /* Esattamente metà dell'indicatore */
        min-height: 0 \!important; /* Permette di adattarsi */
        max-height: 50% \!important; /* Limita al 50% dell'altezza */
        flex: 0 0 50% \!important; /* Non cresce né si restringe, rimane al 50% */
        position: absolute \!important; /* Posizionamento assoluto */
        top: 0 \!important; /* Allineato all'inizio */
        width: 100% \!important; /* Larghezza completa */
    }

    body #advantage-indicator .advantage-black {
        border-radius: 0 \!important;
        height: 50% \!important; /* Esattamente metà dell'indicatore */
        min-height: 0 \!important; /* Permette di adattarsi */
        max-height: 50% \!important; /* Limita al 50% dell'altezza */
        flex: 0 0 50% \!important; /* Non cresce né si restringe, rimane al 50% */
        position: absolute \!important; /* Posizionamento assoluto */
        bottom: 0 \!important; /* Allineato al fondo */
        width: 100% \!important; /* Larghezza completa */
    }

    /* Etichette di vantaggio */
    .advantage-label {
        font-size: 12px \!important;
        font-weight: 700 \!important;
        text-shadow: 0 0 3px rgba(0,0,0,0.7) \!important;
    }

    /* Layout elementi nel board area */
    #game-board {
        flex: 1 \!important;
        margin-right: 10px \!important;
        height: auto \!important;
        align-self: center \!important;
        margin: 0 auto \!important;
    }

    #board-sidebar {
        width: 350px \!important;
        flex-shrink: 0 \!important;
    }

    /* Animazione vittoria */
    @keyframes victoryGlow {
        0% { box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.4), 0 0 15px rgba(255, 215, 0, 0.3) \!important; }
        100% { box-shadow: inset 0 0 20px rgba(255, 215, 0, 0.7), 0 0 30px rgba(255, 215, 0, 0.6) \!important; }
    }
}

/* Miglioramenti per l'animazione delle carte */
.hand-area .card-slot > * {
    transition: none;
    will-change: auto;
}

/* Garantisce che le carte appaiano correttamente solo dopo l'animazione */
@keyframes fadeInCard {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* Stili per l'overlay di riconnessione */
#reconnection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fade-in 0.3s ease-in-out;
}

#reconnection-overlay.fade-out {
    animation: fade-out 0.5s ease-in-out forwards;
}

.reconnection-content {
    background-color: rgba(10, 20, 35, 0.9);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 0 30px rgba(50, 100, 255, 0.5);
    border: 1px solid rgba(100, 150, 255, 0.3);
    backdrop-filter: blur(10px);
}

.reconnection-content h3 {
    color: #ff4d4d;
    margin-top: 0;
    font-size: 1.4em;
    text-shadow: 0 0 10px rgba(255, 50, 50, 0.5);
}

.reconnection-content p {
    color: #e0f0ff;
    margin: 15px 0;
    font-size: 1.1em;
}

.reconnection-progress-bar {
    width: 100%;
    height: 10px;
    background-color: rgba(50, 50, 50, 0.5);
    border-radius: 5px;
    margin: 15px 0;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
}

.reconnection-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff4d4d, #ff6b6b);
    border-radius: 5px;
    transition: width 1s linear;
    box-shadow: 0 0 10px rgba(255, 50, 50, 0.5);
}

#reconnection-countdown {
    font-weight: bold;
    color: #ff4d4d;
    font-size: 1.3em;
    text-shadow: 0 0 10px rgba(255, 50, 50, 0.5);
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fade-out {
    from { opacity: 1; }
    to { opacity: 0; }
}

.warning-flash {
    animation: warningFlash 0.5s 4 alternate;
}

@keyframes warningFlash {
    from {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(255, 50, 50, 0.5);
        box-shadow: 0 0 30px rgba(255, 50, 50, 0.5);
    }
    to {
        transform: scale(1.05);
        text-shadow: 0 0 20px rgba(255, 50, 50, 0.8);
        box-shadow: 0 0 50px rgba(255, 50, 50, 0.8);
    }
}

/* Enhanced notification styles */
.notification {
    display: flex;
    align-items: center;
    background-color: rgba(10, 20, 30, 0.9);
    border-left: 4px solid rgba(50, 150, 255, 0.8);
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(50, 150, 255, 0.3);
}

.notification-icon {
    margin-right: 12px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3), inset 0 0 10px rgba(0, 0, 0, 0.2);
}

.notification-content {
    flex: 1;
}

.notification-content strong {
    display: block;
    margin-bottom: 4px;
    font-size: 1.1em;
    color: #e0f0ff;
}

.notification-content p {
    margin: 0;
    font-size: 0.9em;
    color: #c0d0ff;
}

.notification.notification-warning {
    border-left-color: rgba(255, 200, 50, 0.8);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 200, 50, 0.3);
}

.notification.notification-error {
    border-left-color: rgba(255, 50, 50, 0.8);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 50, 50, 0.3);
}

.notification.notification-success {
    border-left-color: rgba(50, 220, 100, 0.8);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(50, 220, 100, 0.3);
}
/* Media query per comprimere il contenuto su mobile */
@media (max-width: 480px) {
    .main-content {

    }}

    .hero-section,
    .rules-section,
    .features-section,
    .academy-title,
    .academy-subtitle {
        max-width: 100%; /* Permette alle sezioni di occupare tutto lo spazio disponibile su mobile */
    }
/* Media query per comprimere il contenuto su mobile */
@media (max-width: 480px) {
    .main-content {

    }}

    .hero-section,
    .rules-section,
    .features-section,
    .academy-title,
    .academy-subtitle {
        max-width: 100%; /* Permette alle sezioni di occupare più spazio su mobile */
    }
/* Media query per comprimere il contenuto su mobile */
@media (max-width: 768px) {
    .main-content {

    }
}

@media (max-width: 480px) {
    .main-content {

    }

    .hero-section,
    .rules-section,
    .features-section,
    .academy-title,
    .academy-subtitle {
        max-width: 95%; /* Permette alle sezioni di occupare più spazio su mobile */
    }

    /* Riduce le dimensioni della barra di scorrimento su mobile */
    .main-content::-webkit-scrollbar {
        width: 8px;
    }
}

/* ========================================== */
/* BUGFIX FINALE: Regola con massima priorità per vertici neutrali */
/* ========================================== */

/* Questa regola ha la massima specificità e viene applicata per ultima per garantire
   che i vertici neutrali mantengano sempre l'aspetto metà bianco e metà nero */
.vertex[data-controller="none"],
.vertex[data-controller="none"].playable-vertex,
.vertex[data-controller="none"].neutral-vertex,
.vertex[data-controller="none"]:hover,
.vertex[data-controller="none"]:focus {
    background: linear-gradient(to right, #ffffff 50%, #000000 50%) !important;
    background-color: transparent !important;
    background-image: linear-gradient(to right, #ffffff 50%, #000000 50%) !important;
}
