/**
 * Multiplayer functionality for Skèmino
 * Handles matchmaking, game state synchronization, and turn-based gameplay
 * 
 * DETTAGLI DEL FUNZIONAMENTO:
 * 
 * 1. INIZIALIZZAZIONE:
 *    - Quando la pagina /game viene caricata, initMultiplayer() viene chiamata
 *    - Recupera eventuali dati di match salvati in sessionStorage (da home-logged.html)
 *    - Se trova dati salvati, chiama handleMatchFound() per processarli
 *    - Inizializza tutti i listener per eventi Socket.io
 * 
 * 2. FLUSSO DI MATCHMAKING:
 *    - Da home-logged.html: click su "Nuova Partita" → startMatchmaking()
 *    - Invia richiesta 'findMatch' al server
 *    - Server risponde con 'matchFound' → handleMatchFound()
 *    - handleMatchFound() inizializza GameModeManager e mostra animazione dadi
 * 
 * 3. GESTIONE PARTITA:
 *    - Riceve eventi 'gameState' dal server
 *    - Gestisce timer dei turni e timeout
 *    - Sincronizza le mosse tra i giocatori
 *    - Gestisce chat in-game
 *    - Monitora disconnessioni e riconnessioni
 * 
 * 4. INTEGRAZIONE CON ALTRI MODULI:
 *    - Usa GameModeManager per coordinare la modalità online
 *    - Chiama showGameSetup() da script.js per animazioni
 *    - Si interfaccia con online-game.js per logica specifica online
 */


// Esponiamo showVictoryScreen globalmente per poterla richiamare dai timer
if (typeof window.showVictoryScreen !== 'function') {
    window.showVictoryScreen = function(winner, reason) {
        console.log('Mostrando il riquadro di vittoria...');
        const victoryScreen = document.getElementById('victory-screen');

        if (!victoryScreen) {
            console.error('Elemento victory-screen non trovato!');
            return;
        }

        // Imposta il nome del vincitore e il motivo della vittoria
        const winnerNameElement = document.getElementById('winner-name');
        const victoryReasonElement = document.getElementById('victory-reason');

        if (winnerNameElement) {
            winnerNameElement.textContent = winner || 'Giocatore';
        }

        if (victoryReasonElement) {
            victoryReasonElement.textContent = reason || 'Hai vinto la partita!';
        }

        // Assicurati che il riquadro di vittoria sia visibile
        victoryScreen.style.display = 'flex';

        // Riproduci suono di vittoria
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('victory', 0.7);
        }

        console.log('Riquadro di vittoria mostrato.');
    };
}

// Multiplayer state
const multiplayer = {
    isMatchmaking: false,
    currentGameId: null,
    inviteCode: null,
    opponent: null,
    turnTimer: null,
    turnTimeRemaining: 0,
    turnTimeout: 60, // Default timeout in seconds
    notifications: [],
    matchmakingTimeout: null // Timer per il messaggio quando non trova avversari
};

// DOM Elements
let matchmakingModal;
let matchmakingStatus;
let matchmakingCancel;
let createGameBtn;
let joinGameBtn;
let inviteCodeInput;
let turnTimerElement;
let notificationContainer;

// Initialize multiplayer functionality
function initMultiplayer() {
    // Initialize DOM elements
    matchmakingModal = document.getElementById('matchmaking-modal');
    matchmakingStatus = document.getElementById('matchmaking-status');
    matchmakingCancel = document.getElementById('cancel-matchmaking');
    createGameBtn = document.getElementById('create-game-btn');
    joinGameBtn = document.getElementById('join-game-btn');
    inviteCodeInput = document.getElementById('invite-code-input');
    turnTimerElement = document.getElementById('turn-timer');
    notificationContainer = document.getElementById('notification-container');
    
    // Controlla se abbiamo dei dati salvati in sessionStorage
    const savedGameData = sessionStorage.getItem('gameData');
    if (savedGameData) {
        console.log('[MULTIPLAYER] Dati di gioco trovati in sessionStorage:', savedGameData);
        console.log('[MULTIPLAYER] URL corrente:', window.location.href);
        console.log('[MULTIPLAYER] Parametri URL:', window.location.search);

        try {
            const gameData = JSON.parse(savedGameData);
            
            // VERIFICA VALIDITÀ DEI DATI: Controlla se i dati non sono troppo vecchi
            const maxAge = 5 * 60 * 1000; // 5 minuti
            const dataAge = gameData.timestamp ? Date.now() - gameData.timestamp : maxAge + 1;
            
            if (dataAge > maxAge) {
                console.log('[MULTIPLAYER] Dati di gioco troppo vecchi, rimozione automatica');
                sessionStorage.removeItem('gameData');
                return;
            }
            
            // Verifica che i dati abbiano i campi necessari
            if (!gameData.gameId || !gameData.playerId) {
                console.log('[MULTIPLAYER] Dati di gioco incompleti, rimozione automatica');
                sessionStorage.removeItem('gameData');
                return;
            }

            // Salva i dati per processarli quando il socket sarà pronto
            window.pendingMatchData = gameData;
            console.log('[MULTIPLAYER] Dati del match salvati, in attesa della connessione socket');

            // Aggiungi un flag per indicare che stiamo processando dati di gioco
            window.processingGameData = true;
            console.log('[MULTIPLAYER] Flag processingGameData impostato a true');
            
            // Rimuovi i dati solo dopo che sono stati processati
            setTimeout(() => {
                sessionStorage.removeItem('gameData');
                console.log('[MULTIPLAYER] Dati di sessione rimossi dopo processamento');
            }, 3000);
            
        } catch (error) {
            console.error('[MULTIPLAYER] Errore nel parsing dei dati di gioco:', error);
            sessionStorage.removeItem('gameData');
        }
    }

    // Add event listeners
    if (matchmakingCancel) {
        // Rimuoviamo eventuali listener esistenti
        matchmakingCancel.removeEventListener('click', cancelMatchmaking);
        // Aggiungiamo un nuovo listener con una funzione più robusta
        matchmakingCancel.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Cancel button clicked directly');
            cancelMatchmaking();
        });
    }

    if (createGameBtn) {
        createGameBtn.addEventListener('click', createPrivateGame);
    }

    if (joinGameBtn) {
        joinGameBtn.addEventListener('click', joinPrivateGame);
    }

    // Initialize Socket.io event handlers when socket is available
    if (window.socket) {
        initSocketEvents();
    } else {
        // Wait for socket to be available
        console.log('[MULTIPLAYER] Socket non ancora disponibile, attendo...');
        setTimeout(() => {
            initSocketEvents();
        }, 1000);
    }

    // console.log('Multiplayer functionality initialized');
}

// Initialize Socket.io event handlers
function initSocketEvents() {
    // Get socket from window.socket
    const socket = window.socket;

    if (!socket) {
        console.log('[MULTIPLAYER] Socket non ancora disponibile, attendo inizializzazione...');
        // Retry after a short delay, but limit the number of retries
        if (!initSocketEvents.retryCount) {
            initSocketEvents.retryCount = 0;
        }
        initSocketEvents.retryCount++;

        if (initSocketEvents.retryCount < 20) { // Max 10 seconds of retries
            setTimeout(initSocketEvents, 500);
        } else {
            console.warn('[MULTIPLAYER] Socket non disponibile dopo 10 secondi, interrompo i tentativi');
        }
        return;
    }

    // Reset retry counter on successful connection
    initSocketEvents.retryCount = 0;

    // Processa i dati del match pendenti se esistono
    if (window.pendingMatchData) {
        console.log('[MULTIPLAYER] Socket pronto, processando dati del match pendenti');
        const pendingData = window.pendingMatchData;
        window.pendingMatchData = null; // Pulisci i dati pendenti

        // Processa i dati del match ora che il socket è pronto
        setTimeout(() => {
            handleMatchFound(pendingData);
        }, 100); // Piccolo ritardo per assicurarsi che tutto sia inizializzato
    }

    // Matchmaking events
    socket.on('waitingForOpponent', handleWaitingForOpponent);
    
    // Disabilita matchFound handler sulla home page
    if (!window.location.pathname.includes('home') && !window.location.pathname.includes('index')) {
        socket.on('matchFound', handleMatchFound);
    } else {
        console.log('[MULTIPLAYER] Handler matchFound disabilitato su home page');
    }
    
    socket.on('matchmakingCancelled', handleMatchmakingCancelled);

    // Game events
    socket.on('gameCreated', handleGameCreated);
    socket.on('playerJoined', handlePlayerJoined);
    socket.on('gameOver', handleGameOver);
    // Nota: gameState è già gestito in script.js, quindi non lo duplichiamo qui
    
    // Collegamento all'evento gameState per garantire che gli slot delle mani siano sempre creati
    socket.on('gameState:handSlots', function() {
        // Ricreiamo gli slot delle mani dopo ogni aggiornamento di stato
        setTimeout(createHandSlots, 100);
    });

    // Reconnection events
    socket.on('playerDisconnected', handlePlayerDisconnected);
    socket.on('playerReconnected', handlePlayerReconnected);
    socket.on('recoverySuccess', handleRecoverySuccess);
    socket.on('disconnectionWarning', handleDisconnectionWarning);
    socket.on('reconnectionSuccessful', handleReconnectionSuccessful);

    // Notification events
    socket.on('notification', handleNotification);

    // Chat events - RIMOSSO: ora registrato nell'evento connect di script.js quando socket è realmente connesso
    console.log('[CHAT] DEBUG: Chat event listener sarà registrato nell\'evento connect quando socket è connesso');
    console.log('[CHAT] DEBUG: Socket ID in initSocketEvents:', socket.id, '(potrebbe essere undefined)');

    // console.log('[SOCKET] Inizializzati event handler per socket.io');
}

// Questa funzione non serve più in quanto gameState è gestito in script.js
// Manteniamo solo la funzione di aggiornamento dei timer che è specifica del multiplayer
function updateGameStateForMultiplayer(gameState) {
    // Aggiorna visivamente quale giocatore è attivo
    const isPlayer1Turn = gameState.players && gameState.currentPlayerId &&
                        gameState.players[gameState.currentPlayerId]?.color === 'white';
    const isPlayer2Turn = gameState.players && gameState.currentPlayerId &&
                        gameState.players[gameState.currentPlayerId]?.color === 'black';

    // Aggiorna le classi active sui timer totali
    const player1TimerEl = document.getElementById('player1-total-timer');
    const player2TimerEl = document.getElementById('player2-total-timer');

    if (player1TimerEl) {
        if (isPlayer1Turn) player1TimerEl.classList.add('active');
        else player1TimerEl.classList.remove('active');
    }

    if (player2TimerEl) {
        if (isPlayer2Turn) player2TimerEl.classList.add('active');
        else player2TimerEl.classList.remove('active');
    }
}

// Start matchmaking
function startMatchmaking() {
    // Aggiungi log di debug per verificare lo stato dell'autenticazione
    console.log('[MULTIPLAYER AUTH DEBUG] Verifica autenticazione:');
    console.log('- Token presente:', !!localStorage.getItem('token'));
    console.log('- User presente:', !!localStorage.getItem('user'));
    console.log('- authUtils.isLoggedIn():', window.authUtils && window.authUtils.isLoggedIn());

    if (!isUserLoggedIn()) {
        showLoginPrompt('Per giocare online devi effettuare l\'accesso');
        return;
    }

    // Verifica che il socket sia connesso
    if (!window.socket || !window.socket.connected) {
        console.log('[MULTIPLAYER AUTH DEBUG] Socket non connesso, tentativo di reinizializzazione');
        // Reinizializza il socket
        if (window.reinitializeSocket && typeof window.reinitializeSocket === 'function') {
            window.reinitializeSocket();
            // Attendi che il socket si connetta
            setTimeout(() => {
                if (window.socket && window.socket.connected) {
                    console.log('[MULTIPLAYER AUTH DEBUG] Socket riconnesso con successo');
                    // Riprova il matchmaking
                    startMatchmaking();
                } else {
                    console.log('[MULTIPLAYER AUTH DEBUG] Impossibile riconnettere il socket');
                    showError('Errore di connessione al server. Ricarica la pagina e riprova.');
                }
            }, 1000);
            return;
        } else {
            showError('Errore di connessione al server. Ricarica la pagina e riprova.');
            return;
        }
    }

    // Log di debug per confermare l'autenticazione
    console.log('[MULTIPLAYER AUTH DEBUG] Utente autenticato con successo');
    console.log('- User:', localStorage.getItem('user'));

    multiplayer.isMatchmaking = true;

    // Show matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'flex';
        matchmakingStatus.textContent = 'Ricerca di un avversario in corso...';
    }

    // Get user rating
    const userRating = getUserRating();

    // Log socket authentication status
    console.log('[MATCHMAKING DEBUG] Socket authentication status:');
    console.log('- Socket ID:', window.socket.id);
    console.log('- Socket connected:', window.socket.connected);
    console.log('- Auth token present:', !!localStorage.getItem('token'));
    console.log('- Socket auth:', window.socket.auth);

    // Verifica che il socket abbia l'autenticazione
    if (!window.socket.auth || !window.socket.auth.token) {
        console.log('[MATCHMAKING DEBUG] Socket senza token di autenticazione, tentativo di aggiornamento');
        window.socket.auth = { token: localStorage.getItem('token') };
    }

    // Mostra il modale di matchmaking
    if (matchmakingModal) {
        matchmakingModal.style.display = 'flex';
        if (matchmakingStatus) {
            matchmakingStatus.textContent = 'Ricerca avversario in corso...';
        }
    }

    // Send matchmaking request to server
    window.socket.emit('findMatch', {
        rating: userRating,
        username: window.authUtils?.getCurrentUser()?.username || 'Giocatore'
    });

    console.log('Matchmaking started');

    // Imposta un timer di 1 minuto per mostrare messaggio se non trova avversari
    const matchmakingTimeout = setTimeout(() => {
        if (multiplayer.isMatchmaking) {
            // Se dopo 1 minuto è ancora in ricerca, mostra messaggio
            if (matchmakingStatus) {
                matchmakingStatus.textContent = 'Non abbiamo trovato avversari disponibili. Puoi continuare ad attendere o riprovare più tardi.';
            }

            // Mostra anche una notifica
            showNotification('Non ci sono avversari disponibili al momento. Riprova più tardi o continua ad attendere.', 8000);
        }
    }, 60000); // 60 secondi = 1 minuto

    // Memorizza il timeout per poterlo cancellare se necessario
    multiplayer.matchmakingTimeout = matchmakingTimeout;

    // Gestione errori di autenticazione
    window.socket.once('gameError', (errorMessage) => {
        console.log('[MATCHMAKING ERROR]', errorMessage);

        // Cancella il timeout se esiste
        if (multiplayer.matchmakingTimeout) {
            clearTimeout(multiplayer.matchmakingTimeout);
            multiplayer.matchmakingTimeout = null;
        }

        // Nascondi il modale di matchmaking
        if (matchmakingModal) {
            matchmakingModal.style.display = 'none';
        }

        // Se l'errore è relativo all'autenticazione, mostra il prompt di login
        if (errorMessage.includes('autenticato') || errorMessage.includes('login')) {
            // Rimuovi il token e l'utente dal localStorage
            localStorage.removeItem('token');
            localStorage.removeItem('user');

            // Mostra il prompt di login
            showLoginPrompt('Sessione scaduta. Effettua nuovamente l\'accesso per giocare online.');
        } else {
            // Mostra l'errore generico
            showError(errorMessage);
        }

        if (errorMessage.includes('autenticato') || errorMessage.includes('login')) {
            console.log('[MATCHMAKING AUTH ERROR] Problema di autenticazione rilevato, tentativo di risoluzione...');

            // Nascondi il modal di matchmaking
            if (matchmakingModal) {
                matchmakingModal.style.display = 'none';
            }

            // Mostra un messaggio di errore
            showError('Problema di autenticazione rilevato. Ricarica la pagina e riprova.');

            // Suggerisci di effettuare nuovamente il login
            if (confirm('Problema di autenticazione rilevato. Vuoi effettuare nuovamente il login?')) {
                // Pulisci i dati di autenticazione
                localStorage.removeItem('token');
                localStorage.removeItem('user');

                // Reindirizza alla pagina di login
                window.location.href = '/login?returnTo=game';
            }
        }
    });
}

// Cancel matchmaking
function cancelMatchmaking() {
    console.log('Cancel matchmaking button clicked');

    if (!multiplayer.isMatchmaking) {
        console.log('Not in matchmaking state, ignoring cancel request');
        return;
    }

    // Send cancel request to server
    window.socket.emit('cancelMatchmaking');

    // Forziamo l'annullamento del matchmaking anche sul client
    handleMatchmakingCancelled();

    console.log('Matchmaking cancelled');
}

// Create a private game
function createPrivateGame() {
    if (!isUserLoggedIn()) {
        showLoginPrompt('Per creare una partita privata devi effettuare l\'accesso');
        return;
    }

    // Get turn timeout from input
    const turnTimeoutInput = document.getElementById('turn-timeout-input');
    const turnTimeout = turnTimeoutInput ? parseInt(turnTimeoutInput.value) || 60 : 60;

    // Send create game request to server
    window.socket.emit('createPrivateGame', {
        turnTimeout,
        rating: getUserRating()
    });

    // Show loading state
    if (createGameBtn) {
        createGameBtn.disabled = true;
        createGameBtn.textContent = 'Creazione partita...';
    }

    console.log('Creating private game');
}

// Join a private game
function joinPrivateGame() {
    if (!isUserLoggedIn()) {
        showLoginPrompt('Per unirti a una partita privata devi effettuare l\'accesso');
        return;
    }

    // Get invite code from input
    const inviteCode = inviteCodeInput ? inviteCodeInput.value.trim() : '';

    if (!inviteCode) {
        showError('Inserisci un codice di invito valido');
        return;
    }

    // Send join game request to server
    window.socket.emit('joinPrivateGame', {
        inviteCode,
        rating: getUserRating()
    });

    // Show loading state
    if (joinGameBtn) {
        joinGameBtn.disabled = true;
        joinGameBtn.textContent = 'Accesso in corso...';
    }

    console.log('Joining private game with code:', inviteCode);
}

// Handle waiting for opponent
function handleWaitingForOpponent() {
    if (matchmakingStatus) {
        matchmakingStatus.textContent = 'In attesa di un avversario...';
    }

    console.log('Waiting for opponent');
}

// Handle match found
function handleMatchFound(data) {
    // CORREZIONE: Reset flags protezione turno per nuova partita
    window.nameAnimationCompleted = false;
    window.initialTurnPlayerId = null;
    window.turnProtectionStartTime = null;
    console.log('[MATCH FOUND] Reset flags protezione turno per nuova partita');

    // Previeni esecuzioni multiple della stessa partita SOLO se è stata completata
    if (multiplayer.currentGameId === data.gameId && multiplayer.matchFoundCompleted) {
        console.log('[MATCH FOUND] Match già completato per gameId:', data.gameId, '- Skip esecuzione');
        return;
    }

    // Reset observer flag e observer per nuova partita
    window.gameContainerObserverTriggered = false;
    if (window.gameContainerObserver) {
        window.gameContainerObserver.disconnect();
        window.gameContainerObserver = null;
    }

    multiplayer.isMatchmaking = false;
    multiplayer.currentGameId = data.gameId;
    // CORREZIONE CHAT: Imposta anche window.currentGameId per garantire che la chat funzioni
    window.currentGameId = data.gameId;
    // Reset dei flag per nuova partita
    multiplayer.matchFoundCompleted = false;
    window.gameStateRequested = false;
    multiplayer.opponent = data.opponent;

    // Cancella il timeout di ricerca se esiste
    if (multiplayer.matchmakingTimeout) {
        clearTimeout(multiplayer.matchmakingTimeout);
        multiplayer.matchmakingTimeout = null;
    }

    // Hide matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'none';
    }
    
    // APPLICA IMMEDIATAMENTE gli stili online per evitare compressione durante l'animazione
    if (window.applyOnlinePlayInterfaceImmediate) {
        window.applyOnlinePlayInterfaceImmediate();
    }
    
    // Crea gli slot per le carte nelle aree delle mani in modalità multiplayer
    createHandSlots();

    // IMPORTANTE: Imposta prima il playerId persistente
    // Il server invia l'ID esatto che usa per questo giocatore
    window.myPlayerId = data.playerId;
    // Sincronizza anche la variabile locale myPlayerId se esiste
    if (window.myPlayerId && typeof window.myPlayerId !== 'undefined') {
        try {
            // Assegna direttamente invece di usare eval per evitare errori di inizializzazione
            if (typeof myPlayerId !== 'undefined') {
                myPlayerId = window.myPlayerId;
            }
        } catch (error) {
            console.log('[MATCH FOUND] myPlayerId non ancora inizializzato, verrà impostato successivamente');
        }
    }
    // IMPORTANTE: Usa sempre l'ID fornito dal server in data.playerId
    // Il server gestisce internamente la mappatura tra socket.id e playerId persistente
    // Non dobbiamo sovrascrivere questo ID con socket.id perché causerebbe inconsistenze

    // Mantieni l'ID originale dal server - NON usare socket.id
    // Il server si occupa della sincronizzazione interna

    // Imposta immediatamente i nomi dei giocatori solo se abbiamo nomi reali
    // CORREZIONE: Salviamo immediatamente i dati del giocatore nella memoria permanente
    // per evitare inversioni dei nomi
    const currentUser = window.authUtils?.getCurrentUser();
    const myName = currentUser?.username;
    const opponentName = data.opponent?.name;

    // Memorizza i dati del match globalmente per uso futuro
    window.matchData = {
        myName: myName,
        opponentName: opponentName,
        myColor: data.color,
        opponentColor: data.color === 'white' ? 'black' : 'white'
    };

    // Salva immediatamente nella memorizzazione permanente usando l'ID corretto
    // IMPORTANTE: Usa l'ID attuale (socket.id se disponibile, altrimenti l'ID originale)
    if (window.myPlayerId && myName && myName !== 'Tu') {
        window.permanentPlayerNames[window.myPlayerId] = myName;
        window.permanentPlayerColors[window.myPlayerId] = data.color;
    }
    if (data.opponentId && opponentName && opponentName !== 'Avversario') {
        const opponentColor = data.color === 'white' ? 'black' : 'white';
        window.permanentPlayerNames[data.opponentId] = opponentName;
        window.permanentPlayerColors[data.opponentId] = opponentColor;
    }
    
    // NON impostiamo i nomi nel DOM qui, lasciamo che updatePlayerAreaNames
    // lo faccia correttamente quando arriva il gameState

    // Verifica che il socket sia disponibile (meno restrittivo per dati pendenti)
    if (!window.socket) {
        console.error('[MATCH FOUND ERROR] Socket non disponibile durante matchFound');
        showError('Errore di connessione al server. Ricarica la pagina e riprova.');
        return;
    }

    // Se il socket non è connesso, aspetta un momento per la connessione
    if (!window.socket.connected) {
        console.log('[MATCH FOUND] Socket non ancora connesso, aspetto la connessione...');
        setTimeout(() => {
            if (window.socket.connected && !multiplayer.matchFoundCompleted) {
                console.log('[MATCH FOUND] Socket ora connesso, continuo con handleMatchFound');
                // Richiama la funzione ricorsivamente ora che il socket è connesso
                handleMatchFound(data);
            } else if (multiplayer.matchFoundCompleted) {
                console.log('[MATCH FOUND] Match già completato, skip seconda esecuzione');
            } else {
                console.error('[MATCH FOUND ERROR] Socket ancora non connesso dopo attesa');
                showError('Errore di connessione al server. Ricarica la pagina e riprova.');
            }
        }, 500);
        return;
    }

    // Assicurati che il gameId sia salvato nel socket
    window.socket.gameId = data.gameId;

    // Aggiorna il rating dell'avversario nell'interfaccia
    if (typeof window.updatePlayerRatings === 'function') {
        window.updatePlayerRatings();
    } else {
        // Fallback se la funzione non è disponibile
        const player2RatingElement = document.getElementById('player2-rating');
        if (player2RatingElement && data.opponent && data.opponent.rating) {
            player2RatingElement.textContent = `(${data.opponent.rating})`;
        }
    }

    // Aggiorna direttamente l'elemento del rating del secondo giocatore
    updateOpponentRatingDisplay();

    // Mostra la chat e assicurati che sia configurata per il multiplayer
    const chatArea = document.getElementById('chat-area');
    if (chatArea) {
        chatArea.style.display = 'block';
        chatArea.classList.add('multiplayer-active');
        console.log('[CHAT] Chat abilitata per il multiplayer');
        
        // Forza aggiornamento immediato della visibilità della chat
        setTimeout(() => {
            updateChatVisibility();
            console.log('[CHAT] Visibilità chat aggiornata dopo match found');
        }, 100);
    }
    
    // Assicurati che gli event listener per la chat siano configurati
    setTimeout(() => {
        ensureChatListeners();
    }, 1000); // Piccolo ritardo per assicurarsi che il socket sia completamente inizializzato

    // Prepara l'area chat per la partita
    chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        // Pulisci eventuali messaggi precedenti
        chatMessages.innerHTML = '';
    }

    // Imposta la modalità online nel game mode manager PRIMA di altre inizializzazioni
    window.isOnlineMode = true;

    // Inizializza il game mode manager per partite online se presente
    console.log('[HANDLE MATCH FOUND] Debug GameModeManager:');
    console.log('[HANDLE MATCH FOUND] - window.gameModeManager exists:', !!window.gameModeManager);
    console.log('[HANDLE MATCH FOUND] - window.myPlayerId:', window.myPlayerId);
    
    if (window.gameModeManager) {
        console.log('[HANDLE MATCH FOUND] GameModeManager disponibile, inizializzo...');
        // Assicurati che il socket sia inizializzato nel game mode manager
        if (!window.gameModeManager.socket) {
            console.log('[HANDLE MATCH FOUND] Inizializzo socket nel GameModeManager');
            window.gameModeManager.initialize(window.socket);
        } else {
            console.log('[HANDLE MATCH FOUND] Socket già presente nel GameModeManager');
        }
        
        console.log('[HANDLE MATCH FOUND] Chiamando startOnlineGame con myPlayerId:', window.myPlayerId);
        window.gameModeManager.startOnlineGame(window.myPlayerId);
        console.log('[HANDLE MATCH FOUND] startOnlineGame chiamato');
    } else {
        console.error('[HANDLE MATCH FOUND] ERRORE: window.gameModeManager NON DISPONIBILE!');
    }
    
    // NUOVO APPROCCIO: Transizione fluida senza reset visuale
    
    // Assicurati che gli elementi essenziali siano già nascosti gradualmente
    const homepage = document.getElementById('homepage');
    const mainMenu = document.getElementById('main-menu');
    const playerNamesScreen = document.getElementById('player-names-screen');
    const gameContainer = document.getElementById('game-container');
    
    if (homepage && homepage.style.display !== 'none') {
        homepage.style.transition = 'opacity 0.3s ease-out';
        homepage.style.opacity = '0';
        setTimeout(() => { homepage.style.display = 'none'; }, 300);
    }
    if (mainMenu && mainMenu.style.display !== 'none') {
        mainMenu.style.transition = 'opacity 0.3s ease-out';
        mainMenu.style.opacity = '0';
        setTimeout(() => { mainMenu.style.display = 'none'; }, 300);
    }
    if (playerNamesScreen) playerNamesScreen.style.display = 'none';
    
    // NON mostrare il game container qui - verrà mostrato quando arriva lo stato del gioco
    if (gameContainer) {
        console.log('[MATCH FOUND] Preparo container senza mostrarlo - sarà visualizzato quando arriva lo stato');
        
        // Assicurati che il container sia nascosto e in attesa
        gameContainer.style.display = 'none';
        gameContainer.style.opacity = '0';
        gameContainer.style.transition = 'opacity 0.5s ease-in-out';
        
        // NON aggiungere ready-for-play qui - sarà aggiunto quando l'interfaccia è pronta
        console.log('[MATCH FOUND] Container preparato ma nascosto per evitare refresh doppio');
    }
    
    // Solo per la prima volta, chiama showGameSetup con modalità fluida
    if (!window.gameSetupCalled && typeof window.showGameSetup === 'function') {
        console.log('[MATCH FOUND] Preparando setup senza mostrare interfaccia prematuramente');
        window.gameSetupCalled = true;
        window.smoothTransitionMode = true; // Flag per modalità transizione fluida
        // NON chiamare showGameSetup qui - sarà chiamato quando arriva lo stato del gioco
        // window.showGameSetup();
    } else if (window.gameSetupCalled) {
        console.log('[MATCH FOUND] Setup già completato, skip');
    }
    
    // CORREZIONE: Reset completo di tutti i flag per le animazioni
    window.expectingInitialGameState = true;
    window.hasReceivedInitialState = false;
    window.diceAnimationShown = false; // Reset anche il flag animazione dadi
    window.diceAnimationCompletedOnce = false; // Reset il flag animazione completata
    window.animationsCompletedForGame = null; // Reset flag animazioni completate
    window.isSetupAnimating = false; // Reset flag setup per permettere l'avvio
    window.gameSetupCalled = false; // Reset flag setup chiamato
    window.initialAnimationCompleted = false; // Reset animazione iniziale completata
    
    console.log('[MATCH FOUND] Flag animazioni resettati per nuova partita');
    
    // Assicuriamoci che il socket sia configurato correttamente
    window.socket.gameId = data.gameId;
    
    // Verifica che l'utente sia loggato
    const loggedUser = window.authUtils?.getCurrentUser();
    if (!loggedUser) {
        console.error('[MATCH FOUND] Utente non loggato!');
        showError('Errore: utente non autenticato');
        return;
    }
    
    console.log('[MATCH FOUND] Socket configurato:', {
        gameId: window.socket.gameId,
        myPlayerId: window.myPlayerId,
        userId: loggedUser.id,
        userName: loggedUser.username
    });
    
    // Prima di richiedere lo stato, uniamoci alla room del gioco con più informazioni
    const joinData = {
        gameId: data.gameId,
        userId: loggedUser.id,
        userName: loggedUser.username,
        color: data.color,
        socketId: window.socket?.id // Aggiungi il socket ID attuale se disponibile
    };
    console.log('[MATCH FOUND] Entrando nella room del gioco con dati:', joinData);
    window.socket.emit('joinGame', joinData);

    // Aggiungi un breve ritardo prima di richiedere lo stato del gioco
    // per assicurarsi che il server abbia avuto il tempo di inizializzare la partita
    setTimeout(() => {
        // Richiedi lo stato SOLO se non è già stato richiesto dall'observer
        if (!window.gameStateRequested) {
            window.gameStateRequested = true; // Previeni richieste multiple

            const stateRequest = {
                gameId: data.gameId,
                userId: window.authUtils?.getCurrentUser()?.id,
                color: data.color,
                socketId: window.socket?.id, // Usa il socket ID attuale se disponibile
                playerId: window.myPlayerId // Aggiungi anche il player ID attuale
            };

            console.log('[MATCH FOUND] Richiesta stato del gioco con info complete:', stateRequest);
            window.socket.emit('requestGameState', stateRequest);

            // MULTIPLAYER FIX: Secondo tentativo solo se non abbiamo ancora ricevuto lo stato
            setTimeout(() => {
                if (multiplayer.currentGameId && !window.isGameRunning && !window.hasReceivedInitialState) {
                    console.log('[MATCH FOUND] Secondo tentativo di richiesta stato (nessuno stato ricevuto):', stateRequest);
                    window.socket.emit('requestGameState', stateRequest);
                } else {
                    console.log('[MATCH FOUND] Skip secondo tentativo: gameRunning=', window.isGameRunning, 'hasInitialState=', window.hasReceivedInitialState);
                }
            }, 2000);
        } else {
            console.log('[MATCH FOUND] Stato già richiesto dall\'observer, skip richiesta duplicata');
        }
    }, 1000);

    // Show match found notification
    // showNotification(`Partita trovata! Avversario: ${data.opponent.name} (${data.opponent.rating})`);

    // Marca il match come completato per prevenire esecuzioni multiple
    multiplayer.matchFoundCompleted = true;
    
    // Pulisci il flag di processing per evitare interferenze future
    setTimeout(() => {
        window.processingGameData = false;
        console.log('[MATCH FOUND] Flag processingGameData pulito');
    }, 2000);

    console.log('[MATCH FOUND] Match trovato e processato:', data);
    // [MATCH FOUND] Fine processamento match
}

// Handle matchmaking cancelled
function handleMatchmakingCancelled() {
    multiplayer.isMatchmaking = false;

    // Cancella il timeout di ricerca se esiste
    if (multiplayer.matchmakingTimeout) {
        clearTimeout(multiplayer.matchmakingTimeout);
        multiplayer.matchmakingTimeout = null;
    }

    // Hide matchmaking modal
    if (matchmakingModal) {
        matchmakingModal.style.display = 'none';
    }

    // Reset UI
    if (matchmakingStatus) {
        matchmakingStatus.textContent = 'Ricerca annullata';
    }

    console.log('Matchmaking cancelled');
}

// Handle game created
function handleGameCreated(data) {
    multiplayer.currentGameId = data.gameId;
    multiplayer.inviteCode = data.inviteCode;

    // Reset UI
    if (createGameBtn) {
        createGameBtn.disabled = false;
        createGameBtn.textContent = 'Crea Partita Privata';
    }

    // Show invite code
    const inviteCodeDisplay = document.getElementById('invite-code-display');
    if (inviteCodeDisplay) {
        inviteCodeDisplay.textContent = data.inviteCode;
        inviteCodeDisplay.parentElement.style.display = 'block';
    }

    // Show notification
    showNotification(`Partita privata creata! Codice invito: ${data.inviteCode}`);

    console.log('Game created:', data);
}

// Handle player joined
function handlePlayerJoined(data) {
    // Show notification
    showNotification(`${data.playerName} si è unito alla partita!`);

    // Reset UI
    if (joinGameBtn) {
        joinGameBtn.disabled = false;
        joinGameBtn.textContent = 'Unisciti';
    }

    console.log('Player joined:', data);
}

// Handle game over
function handleGameOver(data) {
    // Stop turn timer
    stopTurnTimer();

    // Show rating changes if available
    if (data.ratingUpdates) {
        const { winner, loser } = data.ratingUpdates;

        // Check if current user is winner
        const isWinner = winner.userId === getCurrentUserId();

        // Show appropriate message
        if (isWinner) {
            showNotification(`Hai vinto! Rating: ${winner.oldRating} → ${winner.newRating} (${winner.change > 0 ? '+' : ''}${winner.change})`);
        } else {
            showNotification(`Hai perso. Rating: ${loser.oldRating} → ${loser.newRating} (${loser.change > 0 ? '+' : ''}${loser.change})`);
        }

        // Check for level up
        if (winner.oldLevel && winner.level && winner.oldLevel.name !== winner.level.name) {
            if (isWinner) {
                showLevelUpNotification(winner.oldLevel, winner.level);
            } else {
                // Show opponent level up
                showNotification(`Il tuo avversario è salito di livello: ${winner.oldLevel.name} → ${winner.level.name}!`);
            }
        }

        // Check for level down
        if (loser.oldLevel && loser.level && loser.oldLevel.name !== loser.level.name) {
            if (!isWinner) {
                showLevelDownNotification(loser.oldLevel, loser.level);
            }
        }

        // Salva le informazioni di aggiornamento rating nello stato del gioco per la schermata di vittoria
        if (window.currentGameState) {
            window.currentGameState.ratingUpdates = data.ratingUpdates;
        }
    }

    // CORREZIONE: Non mostrare pannello di vittoria qui perché è già gestito da victory-fix.js
    // Il victory-fix.js mostra la schermata con dettagli completi tramite updateGameUI()
    console.log('[VICTORY] Victory screen gestito da victory-fix.js - skip gestione qui');
    
    // Verifica se il victory screen è già visibile (gestito da victory-fix.js)
    setTimeout(() => {
        const victoryScreen = document.getElementById('victory-screen');
        if (victoryScreen && victoryScreen.style.display === 'flex') {
            console.log('[VICTORY] Victory screen già mostrato da victory-fix.js con dettagli completi');
        } else {
            console.log('[VICTORY] Victory screen non mostrato, fallback emergency');
            // Solo fallback di emergenza se victory-fix.js non ha funzionato
            if (typeof showVictoryScreen === 'function') {
                let winnerName = 'Giocatore';
                if (window.currentGameState && window.currentGameState.playerNames && data.winnerId) {
                    winnerName = window.currentGameState.playerNames[data.winnerId] || 'Giocatore';
                }
                showVictoryScreen(winnerName, data.winReason || 'Partita completata');
            }
        }
    }, 100); // Controllo molto veloce per verificare se victory-fix.js ha già mostrato la schermata

    console.log('Game over:', data);
}

// Handle notification
function handleNotification(data) {
    // Add to notifications list
    multiplayer.notifications.push(data);

    // Show notification
    showNotification(data.message);

    console.log('Notification received:', data);
}

// Start turn timer
function startTurnTimer(seconds) {
    // Check if game is ready for play (animation is complete)
    const gameContainerElement = document.getElementById('game-container');
    const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

    if (!isGameReady) {
        console.log('[TIMER] Non avvio il timer di turno perché l\'animazione è ancora in corso');
        return; // Don't start the timer if game is not ready
    }

    // Stop existing timer
    stopTurnTimer();

    // Set initial time
    multiplayer.turnTimeRemaining = seconds || multiplayer.turnTimeout;

    // Update timer display
    updateTurnTimerDisplay();

    console.log('[TIMER] Avvio timer di turno: ' + multiplayer.turnTimeRemaining + ' secondi rimanenti');

    // Start timer
    multiplayer.turnTimer = setInterval(() => {
        multiplayer.turnTimeRemaining--;

        // Update timer display
        updateTurnTimerDisplay();

        // Solo ferma il timer se scade
        if (multiplayer.turnTimeRemaining <= 0) {
            stopTurnTimer();
            // La partita continua anche se il tempo di turno è scaduto
        }
    }, 1000);
}

// Stop turn timer
function stopTurnTimer() {
    if (multiplayer.turnTimer) {
        clearInterval(multiplayer.turnTimer);
        multiplayer.turnTimer = null;
    }
}

// Update turn timer display
function updateTurnTimerDisplay() {
    if (!turnTimerElement) return;

    // Format time
    const minutes = Math.floor(multiplayer.turnTimeRemaining / 60);
    const seconds = multiplayer.turnTimeRemaining % 60;
    const formattedTime = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    // Update display
    turnTimerElement.textContent = formattedTime;

    // Add warning class if time is running low
    if (multiplayer.turnTimeRemaining <= 10) {
        turnTimerElement.classList.add('warning');
    } else {
        turnTimerElement.classList.remove('warning');
    }
}

// Show notification
function showNotification(message, duration = 5000, type = 'default') {
    if (!notificationContainer) return;

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Check if it's a disconnection notification
    if (message.includes('disconnesso') || message.includes('riconnesso')) {
        // Create enhanced notification with icon
        notification.innerHTML = `
            <div class="notification-icon">
                ${message.includes('disconnesso') ?
                    '<i class="fas fa-wifi" style="color: #ff4d4d;"></i>' :
                    '<i class="fas fa-plug" style="color: #50d080;"></i>'}
            </div>
            <div class="notification-content">
                <strong>${message.includes('disconnesso') ? 'Disconnessione' : 'Riconnessione'}</strong>
                <p>${message}</p>
            </div>
        `;
    } else {
        // Regular notification
        notification.textContent = message;
    }

    // Add to container
    notificationContainer.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Remove after duration
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);

    // Also log to console for debugging
    console.log(`[NOTIFICATION] ${message}`);

    return notification;
}

// Show level up notification
function showLevelUpNotification(oldLevel, newLevel) {
    // Create level up modal
    const levelUpModal = document.createElement('div');
    levelUpModal.className = 'level-up-modal';
    levelUpModal.innerHTML = `
        <div class="level-up-content">
            <h2>Livello Aumentato!</h2>
            <div class="level-change">
                <div class="old-level">
                    <img src="img/ranks/${oldLevel.avatar}" alt="${oldLevel.name}">
                    <p>${oldLevel.name}</p>
                </div>
                <div class="level-arrow">→</div>
                <div class="new-level">
                    <img src="img/ranks/${newLevel.avatar}" alt="${newLevel.name}">
                    <p>${newLevel.name}</p>
                </div>
            </div>
            <button class="close-btn">Continua</button>
        </div>
    `;

    // Add to body
    document.body.appendChild(levelUpModal);

    // Show modal
    setTimeout(() => {
        levelUpModal.classList.add('show');
    }, 10);

    // Add close button event
    const closeBtn = levelUpModal.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            levelUpModal.classList.remove('show');
            setTimeout(() => {
                levelUpModal.remove();
            }, 300);
        });
    }

    // Play level up sound
    if (gameAudio && gameAudio.play) {
        gameAudio.play('levelUp', 0.7);
    }
}

// Show level down notification
function showLevelDownNotification(oldLevel, newLevel) {
    // Show notification
    showNotification(`Sei sceso di livello: ${oldLevel.name} → ${newLevel.name}.`, 8000);
}

// Handle player disconnection
function handlePlayerDisconnected(data) {
    console.log('[DISCONNECT] Opponent disconnected:', data);

    // Create a countdown overlay
    const countdownOverlay = document.createElement('div');
    countdownOverlay.id = 'reconnection-overlay';
    countdownOverlay.innerHTML = `
        <div class="reconnection-content">
            <h3>Avversario disconnesso</h3>
            <p>L'avversario ha <span id="reconnection-countdown">${data.reconnectionTimeLeft}</span> secondi per riconnettersi</p>
            <div class="reconnection-progress-bar">
                <div class="reconnection-progress" id="reconnection-progress"></div>
            </div>
            <p>Il tempo continua a scorrere</p>
        </div>
    `;
    document.body.appendChild(countdownOverlay);

    // Update countdown every second
    const maxTime = data.reconnectionTimeLeft;
    let timeLeft = data.reconnectionTimeLeft;
    const countdownElement = document.getElementById('reconnection-countdown');
    const progressElement = document.getElementById('reconnection-progress');

    if (progressElement) {
        progressElement.style.width = '100%';
    }

    // Play disconnect sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('error', 0.7);
    }

    // Start countdown
    const countdownInterval = setInterval(() => {
        timeLeft--;

        if (countdownElement) {
            countdownElement.textContent = timeLeft;
        }

        if (progressElement) {
            progressElement.style.width = `${(timeLeft / maxTime) * 100}%`;
        }

        // When time is getting low (less than 10 seconds), add warning class
        if (timeLeft <= 10 && countdownElement) {
            countdownElement.classList.add('warning-flash');
            progressElement.classList.add('warning-flash');
        }

        // Final warning at 5 seconds
        if (timeLeft === 5) {
            showNotification(`L'avversario ha solo 5 secondi per riconnettersi!`, 4000, 'warning');

            // Extra warning sound
            if (window.gameAudio && window.gameAudio.play) {
                window.gameAudio.play('error', 0.5);
            }
        }

        if (timeLeft <= 0) {
            clearInterval(countdownInterval);

            // Show victory message due to opponent timeout
            showNotification('Vittoria! L\'avversario non si è riconnesso in tempo.', 8000, 'success');

            // Remove overlay after animation
            if (countdownOverlay) {
                countdownOverlay.classList.add('fade-out');
                setTimeout(() => {
                    countdownOverlay.remove();
                }, 500);
            }
        }
    }, 1000);

    // Store the interval in a global variable for cleanup
    window.reconnectionCountdown = {
        interval: countdownInterval,
        overlayElement: countdownOverlay
    };

    // Show warning message
    showNotification(`Avversario disconnesso. Ha ${data.reconnectionTimeLeft} secondi per riconnettersi.`, 6000, 'error');
}

// Handle player reconnection
function handlePlayerReconnected(data) {
    console.log('[RECONNECT] Opponent reconnected:', data);

    // Clear any existing reconnection countdown
    if (window.reconnectionCountdown) {
        clearInterval(window.reconnectionCountdown.interval);

        // Remove overlay with fade-out animation
        if (window.reconnectionCountdown.overlayElement) {
            window.reconnectionCountdown.overlayElement.classList.add('fade-out');
            setTimeout(() => {
                window.reconnectionCountdown.overlayElement.remove();
            }, 500);
        }

        window.reconnectionCountdown = null;
    }

    // Play reconnect sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('bonus', 0.7);
    }

    // Show notification solo se non si tratta di una nuova partita
    if (localStorage.getItem('disconnected_game_state')) {
        // Show enhanced notification
        showNotification(`Avversario riconnesso dopo ${data.reconnectionTime} secondi! Il gioco continua.`, 5000, 'success');
    }

    // Request fresh game state just to be sure
    setTimeout(() => {
        if (window.socket && window.socket.connected) {
            window.socket.emit('requestGameState');
        }
    }, 500);
}

// Handle successful reconnection
function handleReconnectionSuccessful(data) {
    console.log('[RECONNECTION] Successful reconnection:', data);
    
    // Update the player ID with the new socket ID
    if (data.newPlayerId) {
        window.myPlayerId = data.newPlayerId;
        console.log('[RECONNECTION] Updated myPlayerId to new socket ID:', window.myPlayerId);
        
        // Also update the online game manager if it exists
        if (window.onlineGameManager) {
            window.onlineGameManager.myPlayerId = data.newPlayerId;
            console.log('[RECONNECTION] Updated onlineGameManager.myPlayerId');
        }
    }
    
    // Update game state
    if (data.gameState) {
        multiplayer.currentGameId = data.gameId;
        // CORREZIONE CHAT: Imposta anche window.currentGameId
        window.currentGameId = data.gameId;
        
        // Process the game state
        if (window.handleGameState && typeof window.handleGameState === 'function') {
            window.handleGameState(data.gameState);
        }
    }
    
    // Show notification
    showNotification('Riconnessione alla partita riuscita! Puoi continuare a giocare.', 3000, 'success');
    
    // Play success sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('bonus', 0.7);
    }
    
    // Mark that we are no longer disconnected
    window.wasDisconnected = false;
}

// Handle successful game state recovery
function handleRecoverySuccess(data) {
    console.log('[RECOVERY] Game state recovered:', data);

    // Show notification only if we were disconnessi e non stiamo avviando una nuova partita
    if (window.wasDisconnected) {
        // Hide any connection error message
        const connectionErrorElement = document.getElementById('connection-error');
        if (connectionErrorElement) {
            connectionErrorElement.style.display = 'none';
        }

        // Play success sound
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('bonus', 0.7);
        }

        // Se non stiamo avviando una nuova partita esplicitamente, mostra notifica di riconnessione
        if (localStorage.getItem('disconnected_game_state')) {
            // Show enhanced notification
            showNotification('Riconnessione alla partita riuscita! Puoi continuare a giocare.', 3000, 'success');
        }
        window.wasDisconnected = false;
    }
}

// Handle disconnection warning
function handleDisconnectionWarning(data) {
    console.log('[WARNING] Disconnection timeout approaching:', data);

    // Play warning sound
    if (window.gameAudio && window.gameAudio.play) {
        window.gameAudio.play('error', 0.5);
    }

    // Show warning notification with remaining time
    showNotification(`L'avversario sta per essere disconnesso. Tempo rimasto: ${data.remainingTime} secondi`, 6000);

    // Flash the reconnection overlay if it exists
    const overlay = document.getElementById('reconnection-overlay');
    if (overlay) {
        overlay.classList.add('warning-flash');

        // Update the countdown
        const countdownElement = document.getElementById('reconnection-countdown');
        if (countdownElement) {
            countdownElement.textContent = data.remainingTime;
            countdownElement.classList.add('warning-flash');
        }

        // Remove warning flash class after animation
        setTimeout(() => {
            overlay.classList.remove('warning-flash');
            if (countdownElement) {
                countdownElement.classList.remove('warning-flash');
            }
        }, 2000);
    }
}

// Get current user profile
function getCurrentUserProfile() {
    // Use authUtils.getCurrentUser() for consistency
    return window.authUtils?.getCurrentUser() || null;
}

// Get user rating
function getUserRating() {
    // Try to get from user profile
    const userProfile = getCurrentUserProfile();
    if (userProfile && userProfile.rating) {
        return userProfile.rating;
    }

    // Default rating
    return 1000;
}

// Check if user is logged in
function isUserLoggedIn() {
    // Verifica sia il token che l'oggetto user per garantire coerenza con authUtils.isLoggedIn()
    return !!(localStorage.getItem('token') && localStorage.getItem('user'));
}

// Show login prompt
function showLoginPrompt(message) {
    // Verifica se l'utente è già loggato secondo authUtils
    if (window.authUtils && window.authUtils.isLoggedIn()) {
        console.log('[LOGIN PROMPT] Utente già loggato secondo authUtils, ma non secondo isUserLoggedIn()');
        console.log('- Token presente:', !!localStorage.getItem('token'));
        console.log('- User presente:', !!localStorage.getItem('user'));

        // Mostra un messaggio di errore e suggerisci di ricaricare la pagina
        showError('Errore di sincronizzazione dello stato di login. Prova a ricaricare la pagina.');
        return;
    }

    // Save the current action in localStorage
    localStorage.setItem('pendingMultiplayerAction', 'startMatchmaking');

    // Create login prompt modal
    const loginPrompt = document.createElement('div');
    loginPrompt.className = 'login-prompt-modal';
    loginPrompt.innerHTML = `
        <div class="login-prompt-content">
            <h2>Accesso Richiesto</h2>
            <p>${message}</p>
            <div class="login-prompt-buttons">
                <button class="login-btn">Accedi</button>
                <button class="register-btn">Registrati</button>
                <button class="cancel-btn">Annulla</button>
            </div>
        </div>
    `;

    // Add to body
    document.body.appendChild(loginPrompt);

    // Show modal
    setTimeout(() => {
        loginPrompt.classList.add('show');
    }, 10);

    // Add button events
    const loginBtn = loginPrompt.querySelector('.login-btn');
    const registerBtn = loginPrompt.querySelector('.register-btn');
    const cancelBtn = loginPrompt.querySelector('.cancel-btn');

    if (loginBtn) {
        loginBtn.addEventListener('click', () => {
            // Add a return URL parameter to redirect back to the game after login
            window.location.href = '/login?returnTo=game';
        });
    }

    if (registerBtn) {
        registerBtn.addEventListener('click', () => {
            // Add a return URL parameter to redirect back to the game after registration
            window.location.href = '/register?returnTo=game';
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            // Clear the pending action
            localStorage.removeItem('pendingMultiplayerAction');

            loginPrompt.classList.remove('show');
            setTimeout(() => {
                loginPrompt.remove();
            }, 300);
        });
    }
}

// Show error message
function showError(message) {
    // Se il messaggio riguarda un errore di sincronizzazione, mostra un pulsante per ricaricare la pagina
    if (message.includes('sincronizzazione dello stato di login')) {
        // Crea una notifica speciale con pulsante di ricarica
        const notification = document.createElement('div');
        notification.className = 'notification error-notification';
        notification.innerHTML = `
            ${message}
            <button class="reload-btn">Ricarica Pagina</button>
        `;

        // Aggiungi al container
        if (notificationContainer) {
            notificationContainer.appendChild(notification);

            // Mostra notifica
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Aggiungi event listener al pulsante
            const reloadBtn = notification.querySelector('.reload-btn');
            if (reloadBtn) {
                reloadBtn.addEventListener('click', () => {
                    window.location.reload();
                });
            }

            // Non rimuovere automaticamente questa notifica
        } else {
            // Fallback se il container non esiste
            alert(message + " Per favore, ricarica la pagina.");
        }
    } else {
        // Per altri errori, usa la notifica standard
        showNotification(message);
    }
}

// Get current user ID
function getCurrentUserId() {
    const userProfile = getCurrentUserProfile();
    return userProfile ? userProfile.id : null;
}

// Execute game action
function executeGameAction(action, actionData) {
    if (!multiplayer.currentGameId) return;

    // Send action to server
    window.socket.emit('gameAction', {
        action,
        gameId: multiplayer.currentGameId,
        actionData
    });
}

// Update game state with turn timer
function updateGameStateWithTurnTimer(gameState) {
    console.log('[TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState:', gameState);
    console.log('[TIMER DEBUG ENTRY] gameState.turnTimeRemaining:', gameState?.turnTimeRemaining);
    console.log('[TIMER DEBUG ENTRY] gameState.currentPlayerId:', gameState?.currentPlayerId);
    console.log('[TIMER DEBUG ENTRY] gameState.mode:', gameState?.mode);
    
    // DEBUG TURN TIMER ridotto per evitare spam console
    // console.log('[TURN TIMER DEBUG] === ANALISI CONTROLLO TURNO ===');
    // console.log('[TURN TIMER DEBUG] gameState.currentPlayerId:', gameState.currentPlayerId);
    // console.log('[TURN TIMER DEBUG] gameState.originalCurrentPlayerId:', gameState.originalCurrentPlayerId);
    // console.log('[TURN TIMER DEBUG] gameState.myPlayerId:', gameState.myPlayerId);
    // console.log('[TURN TIMER DEBUG] window.myPlayerId:', window.myPlayerId);
    // console.log('[TURN TIMER DEBUG] window.socket?.id:', window.socket?.id);

    // CORREZIONE CRITICA: Usa originalCurrentPlayerId per partite online (socket ID originale)
    const mySocketId = window.socket?.id;
    const isMyTurn = gameState.originalCurrentPlayerId ? 
        gameState.originalCurrentPlayerId === window.myPlayerId :
        gameState.currentPlayerId === window.myPlayerId;

    // console.log('[TURN TIMER DEBUG] mySocketId:', mySocketId);
    // console.log('[TURN TIMER DEBUG] isMyTurn (CORRETTO):', isMyTurn);
    // console.log('[TURN TIMER DEBUG] === FINE ANALISI ===');

    // Check if battle animation has completed
    const gameContainerElement = document.getElementById('game-container');
    const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

        // Update turn timer
    if (gameState.turnTimeRemaining !== undefined) {
        multiplayer.turnTimeout = gameState.turnTimeout || 60;

        // Only start timers if game is ready for play (animation completed)
        console.log('[TIMER DEBUG] Controllo condizioni di base per avvio timer:');
        console.log('[TIMER DEBUG] - isGameReady:', isGameReady);
        console.log('[TIMER DEBUG] - isMyTurn:', isMyTurn);
        console.log('[TIMER DEBUG] - gameState.turnTimeRemaining:', gameState.turnTimeRemaining);
        
        if (isGameReady) {
            console.log('[TIMER DEBUG] Game è pronto, controllo turno...');
            if (isMyTurn) {
                console.log('[TIMER DEBUG] È il mio turno, avvio timer con tempo:', gameState.turnTimeRemaining);
                // Start timer with remaining time
                startTurnTimer(gameState.turnTimeRemaining);
            } else {
                console.log('[TIMER DEBUG] Non è il mio turno, fermo timer');
                // Stop timer
                stopTurnTimer();
            }
        } else {
            console.log('[TIMER DEBUG] Game NON è pronto ancora - game-container non ha classe ready-for-play');
            console.log('[TIMER DEBUG] - gameContainerElement exists:', !!gameContainerElement);
            console.log('[TIMER DEBUG] - gameContainerElement classes:', gameContainerElement?.className);
        }

        // Decrementa il timer totale del giocatore attivo
        console.log('[TIMER DEBUG] Controllo condizioni per avvio timer totali:');
        console.log('[TIMER DEBUG] - window:', !!window);
        console.log('[TIMER DEBUG] - window.currentGameState:', !!window.currentGameState);
        console.log('[TIMER DEBUG] - player1TotalTime:', window.player1TotalTime);
        console.log('[TIMER DEBUG] - player2TotalTime:', window.player2TotalTime);
        console.log('[TIMER DEBUG] - gameState.currentPlayerId:', gameState.currentPlayerId);
        
        if (window && window.currentGameState && window.player1TotalTime !== undefined && window.player2TotalTime !== undefined) {
            try {
                // Determina di quale giocatore è il turno (corretto per modalità online)
                let isPlayer1Turn = false;
                let isPlayer2Turn = false;
                
                if (gameState.mode === 'online') {
                    // Per partite online, usa il mapping player1/player2
                    isPlayer1Turn = gameState.currentPlayerId === 'player1';
                    isPlayer2Turn = gameState.currentPlayerId === 'player2';
                    console.log('[TIMER] Modalità online - currentPlayerId:', gameState.currentPlayerId);
                    console.log('[TIMER] isPlayer1Turn (bianco):', isPlayer1Turn, 'isPlayer2Turn (nero):', isPlayer2Turn);
                } else {
                    // Per partite locali, usa la logica originale
                    isPlayer1Turn = gameState.players && gameState.currentPlayerId &&
                                   gameState.players[gameState.currentPlayerId]?.color === 'white';
                    isPlayer2Turn = gameState.players && gameState.currentPlayerId &&
                                   gameState.players[gameState.currentPlayerId]?.color === 'black';
                    console.log('[TIMER] Modalità locale - isPlayer1Turn:', isPlayer1Turn, 'isPlayer2Turn:', isPlayer2Turn);
                }

                // Aggiorna i timer totali (30 minuti) per il giocatore corrente
                if (isPlayer1Turn) {
                    if (!window.player1TimerInterval) {
                        console.log('[TIMER] Avviato timer totale per giocatore 1');
                        window.player1TimerInterval = setInterval(() => {
                            // Verifica ancora che l'animazione sia completata
                            const gameContainerElement = document.getElementById('game-container');
                            const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

                            if (isGameReady) {
                                if (window.player1TotalTime > 0) {
                                    window.player1TotalTime--;

                                    // Aggiorna il display
                                    const player1TotalTimerCountElement = document.querySelector('#player1-total-timer .total-timer-count');
                                    if (player1TotalTimerCountElement) {
                                        const minutes = Math.floor(window.player1TotalTime / 60);
                                        const seconds = window.player1TotalTime % 60;
                                        player1TotalTimerCountElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                    }

                                    // Avvisi per tempo rimanente basso per il giocatore bianco
                                    if (window.player1TotalTime === 300) { // 5 minuti
                                        showNotification('Ti rimangono 5 minuti!', 3000, 'warning');
                                    } else if (window.player1TotalTime === 60) { // 1 minuto
                                        showNotification('Ultimo minuto!', 3000, 'warning');
                                    }
                                } else if (window.player1TotalTime === 0) {
                                    // Tempo esaurito
                                    clearInterval(window.player1TimerInterval);
                                    window.player1TimerInterval = null;

                                    // Trova il nome dell'avversario
                                    let opponentName = 'Avversario';
                                    if (window.currentGameState && window.currentGameState.players) {
                                        const opponentId = Object.keys(window.currentGameState.players).find(id => id !== window.currentGameState.myPlayerId);
                                        if (opponentId && window.currentGameState.playerNames && window.currentGameState.playerNames[opponentId]) {
                                            opponentName = window.currentGameState.playerNames[opponentId];
                                        } else if (window.multiplayer && window.multiplayer.opponent && window.multiplayer.opponent.name) {
                                            opponentName = window.multiplayer.opponent.name;
                                        }
                                    }

                                    // Mostra il riquadro di vittoria per l'avversario con spiegazione dettagliata
                                    if (window.showVictoryScreen) {
                                        const detailedTimeoutReason = `Vittoria per tempo scaduto - Il tempo totale del tuo avversario (30 minuti) è terminato. Questa è una vittoria immediata senza bisogno di soddisfare le condizioni di vittoria ERA1, ERA2, ERA3 o ERA4.`;
                                        window.showVictoryScreen(opponentName, detailedTimeoutReason);
                                    }

                                    // Il server si occuperà di inviare l'evento gameOver
                                }
                            }
                        }, 1000);
                    }

                    // Ferma il timer dell'altro giocatore
                    if (window.player2TimerInterval) {
                        clearInterval(window.player2TimerInterval);
                        window.player2TimerInterval = null;
                    }
                } else if (isPlayer2Turn) {
                    if (!window.player2TimerInterval) {
                        console.log('[TIMER] Avviato timer totale per giocatore 2');
                        window.player2TimerInterval = setInterval(() => {
                            // Verifica ancora che l'animazione sia completata
                            const gameContainerElement = document.getElementById('game-container');
                            const isGameReady = gameContainerElement && gameContainerElement.classList.contains('ready-for-play');

                            if (isGameReady) {
                                if (window.player2TotalTime > 0) {
                                    window.player2TotalTime--;

                                    // Aggiorna il display
                                    const player2TotalTimerCountElement = document.querySelector('#player2-total-timer .total-timer-count');
                                    if (player2TotalTimerCountElement) {
                                        const minutes = Math.floor(window.player2TotalTime / 60);
                                        const seconds = window.player2TotalTime % 60;
                                        player2TotalTimerCountElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                                    }

                                    // Avvisi per tempo rimanente basso per il giocatore nero
                                    if (window.player2TotalTime === 300) { // 5 minuti
                                        showNotification('L\'avversario ha 5 minuti rimanenti', 3000, 'info');
                                    } else if (window.player2TotalTime === 60) { // 1 minuto
                                        showNotification('L\'avversario ha un minuto rimanente', 3000, 'info');
                                    }
                                } else if (window.player2TotalTime === 0) {
                                    // Tempo esaurito
                                    clearInterval(window.player2TimerInterval);
                                    window.player2TimerInterval = null;

                                    // Trova il tuo nome
                                    let playerName = 'Giocatore';
                                    if (window.currentGameState && window.currentGameState.myPlayerId &&
                                        window.currentGameState.playerNames && window.currentGameState.playerNames[window.currentGameState.myPlayerId]) {
                                        playerName = window.currentGameState.playerNames[window.currentGameState.myPlayerId];
                                    } else if (localStorage.getItem('user')) {
                                        try {
                                            const userData = JSON.parse(localStorage.getItem('user'));
                                            if (userData && userData.username) {
                                                playerName = userData.username;
                                            }
                                        } catch (e) {
                                            console.error('Errore nel parsing dei dati utente:', e);
                                        }
                                    }

                                    // Mostra il riquadro di vittoria con spiegazione dettagliata
                                    if (window.showVictoryScreen) {
                                        const detailedTimeoutReason = `Vittoria per tempo scaduto - Il tempo totale del tuo avversario (30 minuti) è terminato. Questa è una vittoria immediata senza bisogno di soddisfare le condizioni di vittoria ERA1, ERA2, ERA3 o ERA4.`;
                                        window.showVictoryScreen(playerName, detailedTimeoutReason);
                                    }

                                    // Il server si occuperà di inviare l'evento gameOver
                                }
                            }
                        }, 1000);
                    }

                    // Ferma il timer dell'altro giocatore
                    if (window.player1TimerInterval) {
                        clearInterval(window.player1TimerInterval);
                        window.player1TimerInterval = null;
                    }
                }
            } catch (error) {
                console.error('[TIMER] Errore nell\'aggiornamento dei timer totali:', error);
            }
        } else {
            console.log('[TIMER] CONDIZIONI NON SODDISFATTE - timer non avviato');
            console.log('[TIMER] Analisi condizioni fallite:');
            console.log('[TIMER] - window:', !!window);
            console.log('[TIMER] - window.currentGameState:', !!window.currentGameState);
            console.log('[TIMER] - player1TotalTime defined:', window.player1TotalTime !== undefined);
            console.log('[TIMER] - player2TotalTime defined:', window.player2TotalTime !== undefined);
            console.log('[TIMER] - player1TotalTime value:', window.player1TotalTime);
            console.log('[TIMER] - player2TotalTime value:', window.player2TotalTime);
            console.log('[TIMER] La partita non è ancora pronta (animazione in corso). I timer partiranno dopo l\'animazione.');
            // Stop any existing timers while animation is running
            stopTurnTimer();
            if (window.player1TimerInterval) {
                clearInterval(window.player1TimerInterval);
                window.player1TimerInterval = null;
            }
            if (window.player2TimerInterval) {
                clearInterval(window.player2TimerInterval);
                window.player2TimerInterval = null;
            }
        }
    }

    // Aggiorna i rating dei giocatori se l'avversario è disponibile
    try {
        if (multiplayer.opponent && multiplayer.opponent.rating) {
            const player2RatingElement = document.getElementById('player2-rating');
            if (player2RatingElement) {
                player2RatingElement.textContent = `(${multiplayer.opponent.rating})`;
                console.log('[GAMESTATE] Rating giocatore 2 aggiornato:', multiplayer.opponent.rating);
            }
        }
    } catch (error) {
        console.error('[GAMESTATE] Errore nell\'aggiornamento del rating del giocatore 2:', error);
    }

    return gameState;
}

// Initialize on page load
/**
 * Crea gli slot vuoti per le carte nelle aree delle mani dei giocatori
 * Questa funzione assicura che ci siano sempre 10 slot (2 righe da 5) visibili
 * nelle aree delle mani dei giocatori in modalità multiplayer
 */
function createHandSlots() {
    console.log('[HAND SLOTS] Creazione slot per le carte nelle aree delle mani');
    
    // Configurazione
    const MAX_SLOTS = 10; // 2 righe da 5 slot
    const SLOTS_PER_ROW = 5;
    
    // Elemento della mano del giocatore 1 (bianco)
    const player1HandElement = document.getElementById('player1-hand');
    if (player1HandElement) {
        // Pulisci l'area della mano
        player1HandElement.innerHTML = '';
        
        // Crea gli slot vuoti
        for (let i = 0; i < MAX_SLOTS; i++) {
            const slot = document.createElement('div');
            slot.className = 'card-slot empty-slot';
            slot.dataset.index = i;
            slot.setAttribute('role', 'button');
            slot.setAttribute('aria-label', `Slot carta ${i+1} del giocatore 1`);
            player1HandElement.appendChild(slot);
        }
        console.log('[HAND SLOTS] Creati slot per la mano del giocatore 1');
    } else {
        console.warn('[HAND SLOTS] Elemento player1-hand non trovato');
    }
    
    // Elemento della mano del giocatore 2 (nero)
    const player2HandElement = document.getElementById('player2-hand');
    if (player2HandElement) {
        // Pulisci l'area della mano
        player2HandElement.innerHTML = '';
        
        // Crea gli slot vuoti
        for (let i = 0; i < MAX_SLOTS; i++) {
            const slot = document.createElement('div');
            slot.className = 'card-slot empty-slot';
            slot.dataset.index = i;
            slot.setAttribute('role', 'button');
            slot.setAttribute('aria-label', `Slot carta ${i+1} del giocatore 2`);
            player2HandElement.appendChild(slot);
        }
        console.log('[HAND SLOTS] Creati slot per la mano del giocatore 2');
    } else {
        console.warn('[HAND SLOTS] Elemento player2-hand non trovato');
    }
}

// Traccia errori JavaScript che potrebbero causare reload
window.addEventListener('error', function(e) {
    console.error('[ERROR] Errore JavaScript rilevato:', e.error);
    console.error('[ERROR] Messaggio:', e.message);
    console.error('[ERROR] File:', e.filename);
    console.error('[ERROR] Linea:', e.lineno);
    console.error('[ERROR] Stack:', e.error?.stack);
});

// Traccia errori di Promise non gestiti
window.addEventListener('unhandledrejection', function(e) {
    console.error('[PROMISE ERROR] Promise rejection non gestita:', e.reason);
    console.error('[PROMISE ERROR] Stack:', e.reason?.stack);
});

// Traccia i reload della pagina e pulisci le animazioni
window.addEventListener('beforeunload', function(e) {
    console.log('[NAVIGATION CLEANUP] Browser in chiusura/navigazione - pulizia completa animazioni');
    
    // Pulizia emergenziale di tutte le animazioni
    if (window.animationSystem && typeof window.animationSystem.emergencyCleanup === 'function') {
        window.animationSystem.emergencyCleanup();
    }
    
    // Pulizia manuale aggiuntiva degli elementi di animazione
    const animationElements = document.querySelectorAll([
        '.card-animation-container',
        '.card-deal-animation',
        '.animating-card',
        '.animation-pending',
        '[style*="position: absolute"][style*="z-index"]',
        '.card-trail',
        '.particle-trail'
    ].join(', '));
    
    let removedCount = 0;
    animationElements.forEach(element => {
        try {
            // Ferma tutte le animazioni in corso
            const animations = element.getAnimations ? element.getAnimations() : [];
            animations.forEach(anim => {
                try {
                    anim.cancel();
                } catch (animError) {}
            });
            
            // Rimuovi l'elemento
            element.remove();
            removedCount++;
        } catch (e) {
            console.warn('[NAVIGATION CLEANUP] Errore nella rimozione di un elemento:', e);
        }
    });
    
    if (removedCount > 0) {
        console.log(`[NAVIGATION CLEANUP] Rimossi ${removedCount} elementi di animazione`);
    }
    
    // Ferma tutti i timer e intervalli delle animazioni
    if (window.cardSoundInterval) {
        clearInterval(window.cardSoundInterval);
        window.cardSoundInterval = null;
    }
    
    if (window.currentCardSound) {
        try {
            window.currentCardSound.pause();
            window.currentCardSound.currentTime = 0;
        } catch (e) {}
        window.currentCardSound = null;
    }
    
    // Pulisci i timer di animazione se esistono
    ['initialAnimationTimeout', 'cardDealingTimeout', 'battleAnimationTimeout'].forEach(timerName => {
        if (window[timerName]) {
            clearTimeout(window[timerName]);
            window[timerName] = null;
        }
    });
    
    // Reset flag di animazione
    window.isSetupAnimating = false;
    if (window.animationState) {
        window.animationState.needsRestart = false;
        window.animationState.cardDealingInProgress = false;
        window.animationState.initialCardPlacementInProgress = false;
        window.animationState.battleAnimation = { inProgress: false };
    }
    
    console.log('[NAVIGATION CLEANUP] Pulizia completata - elementi rimossi:', removedCount);
});

// Fallback con pagehide per browser che non supportano beforeunload correttamente
window.addEventListener('pagehide', function(event) {
    console.log('[NAVIGATION CLEANUP PAGEHIDE] Pagina nascosta - pulizia rapida animazioni');
    
    // Pulizia rapida degli elementi più problematici
    const cardAnimations = document.querySelectorAll('.card-animation-container, .card-deal-animation, .animating-card');
    let removedCount = 0;
    
    cardAnimations.forEach(element => {
        try {
            // Ferma animazioni in corso
            const animations = element.getAnimations ? element.getAnimations() : [];
            animations.forEach(anim => {
                try {
                    anim.cancel();
                } catch (e) {}
            });
            
            element.remove();
            removedCount++;
        } catch (e) {}
    });
    
    // Ferma i suoni
    if (window.currentCardSound) {
        try {
            window.currentCardSound.pause();
        } catch (e) {}
    }
    
    if (window.cardSoundInterval) {
        clearInterval(window.cardSoundInterval);
    }
    
    if (removedCount > 0) {
        console.log(`[NAVIGATION CLEANUP PAGEHIDE] Rimossi ${removedCount} elementi di animazione`);
    }
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione');
    console.log('[MULTIPLAYER] URL:', window.location.href);

    // NON pulire l'URL se abbiamo dati di gioco in sessionStorage
    // perché il sistema di redirect ha bisogno dei parametri per capire che siamo in modalità gioco
    const savedGameData = sessionStorage.getItem('gameData');
    if (window.location.search && !savedGameData) {
        // TEMPORANEAMENTE DISABILITATO - Rimuovi i parametri dall'URL solo se non abbiamo dati di gioco pendenti
        console.log('[MULTIPLAYER] Pulizia URL disabilitata temporaneamente per debug refresh');
        // window.history.replaceState({}, document.title, '/game');
    } else if (savedGameData) {
        console.log('[MULTIPLAYER] Mantengo parametri URL per evitare redirect durante inizializzazione gioco');
    }
    
    // Inizializza le funzionalità multiplayer
    initMultiplayer();

    // Inizializza la chat
    initChat();

    // Crea gli slot per le carte nelle aree delle mani
    // lo facciamo subito per evitare che le aree appaiano vuote
    createHandSlots();
    
    // CORREZIONE CHAT: Controllo periodico della visibilità della chat
    // Aggiorna la chat ogni secondo per assicurarsi che sia sempre attiva quando necessario
    setInterval(() => {
        if (document.getElementById('chat-area')) {
            updateChatVisibility();
        }
    }, 1000);
    
    // Configura un osservatore che monitori il DOM per ricreare gli slot delle mani
    // quando vengono cancellati o quando il contenitore delle mani viene modificato
    let handSlotsCreationInProgress = false; // Flag per evitare loop infiniti

    const handAreasObserver = new MutationObserver(function(mutations) {
        // Evita loop infiniti durante la creazione degli slot
        if (handSlotsCreationInProgress) {
            return;
        }

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                // Verifica se le aree delle mani sono vuote o hanno meno di 10 slot
                const player1HandElement = document.getElementById('player1-hand');
                const player2HandElement = document.getElementById('player2-hand');

                if (player1HandElement && player1HandElement.children.length < 10) {
                    console.log('[OBSERVER] Rilevata modifica dell\'area della mano del giocatore 1, ricreo gli slot');
                    handSlotsCreationInProgress = true;
                    createHandSlots();
                    setTimeout(() => { handSlotsCreationInProgress = false; }, 100);
                }

                if (player2HandElement && player2HandElement.children.length < 10) {
                    console.log('[OBSERVER] Rilevata modifica dell\'area della mano del giocatore 2, ricreo gli slot');
                    handSlotsCreationInProgress = true;
                    createHandSlots();
                    setTimeout(() => { handSlotsCreationInProgress = false; }, 100);
                }
            }
        });
    });
    
    // Osserva le aree delle mani
    const player1HandElement = document.getElementById('player1-hand');
    const player2HandElement = document.getElementById('player2-hand');
    
    if (player1HandElement) {
        handAreasObserver.observe(player1HandElement, { childList: true });
    }
    
    if (player2HandElement) {
        handAreasObserver.observe(player2HandElement, { childList: true });
    }

    // Aggiungi variabili globali per i timer dei giocatori
    if (typeof window.player1TimerInterval === 'undefined') window.player1TimerInterval = null;
    if (typeof window.player2TimerInterval === 'undefined') window.player2TimerInterval = null;

    // Assicurati che i timer totali siano inizializzati (fallback se non sono definiti in script.js)
    if (typeof window.player1TotalTime === 'undefined') window.player1TotalTime = 1800; // 30 minuti
    if (typeof window.player2TotalTime === 'undefined') window.player2TotalTime = 1800; // 30 minuti

    // Log per debug
    // console.log('[TIMER INIT] Inizializzati timer globali per i giocatori');

    // Aggiorna immediatamente il display dei timer totali
    setTimeout(() => {
        const player1TotalTimerCountElement = document.querySelector('#player1-total-timer .total-timer-count');
        const player2TotalTimerCountElement = document.querySelector('#player2-total-timer .total-timer-count');

        if (player1TotalTimerCountElement) {
            const minutes1 = Math.floor(window.player1TotalTime / 60);
            const seconds1 = window.player1TotalTime % 60;
            player1TotalTimerCountElement.textContent = `${minutes1.toString().padStart(2, '0')}:${seconds1.toString().padStart(2, '0')}`;
        }

        if (player2TotalTimerCountElement) {
            const minutes2 = Math.floor(window.player2TotalTime / 60);
            const seconds2 = window.player2TotalTime % 60;
            player2TotalTimerCountElement.textContent = `${minutes2.toString().padStart(2, '0')}:${seconds2.toString().padStart(2, '0')}`;
        }
    }, 500);

    // Monitor game-container for the 'ready-for-play' class being added
    // When detected, refresh the game state to start timers

    // Usa una variabile globale per evitare attivazioni multiple tra diversi observer
    if (!window.gameContainerObserverTriggered) {
        window.gameContainerObserverTriggered = false;
    }

    // Evita di creare observer multipli
    if (window.gameContainerObserver) {
        console.log('[ANIMATION] Observer già esistente, evito di crearne un altro');
        return;
    }

    window.gameContainerObserver = new MutationObserver(function(mutations) {
        // Evita attivazioni multiple usando variabile globale
        if (window.gameContainerObserverTriggered) {
            console.log('[ANIMATION] Observer già attivato, salto questa attivazione');
            return;
        }

        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                mutation.attributeName === 'class' &&
                mutation.target.classList.contains('ready-for-play')) {

                console.log('[ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer');

                // Imposta il flag GLOBALE per evitare attivazioni multiple
                window.gameContainerObserverTriggered = true;
                console.log('[ANIMATION] Flag globale gameContainerObserverTriggered impostato a true');

                // If we have a current game state, re-apply it to start timers
                if (window.currentGameState) {
                    console.log('[ANIMATION] window.currentGameState presente, riapplico lo stato');
                    // Wait a short moment to ensure animation is fully complete
                    setTimeout(() => {
                        console.log('[ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l\'animazione');
                        updateGameStateWithTurnTimer(window.currentGameState);
                    }, 100);
                } else {
                    console.log('[ANIMATION] window.currentGameState NON presente, salto riapplicazione stato');
                }

                // Disconnect observer as we only need this once
                if (window.gameContainerObserver) {
                    window.gameContainerObserver.disconnect();
                    window.gameContainerObserver = null;
                    console.log('[ANIMATION] Observer disconnesso e rimosso');
                } else {
                    console.log('[ANIMATION] Observer già disconnesso, salto operazione');
                }
            }
        });
    });

    // Start observing game-container for class changes
    const gameContainer = document.getElementById('game-container');
    if (gameContainer) {
        window.gameContainerObserver.observe(gameContainer, { attributes: true });
        console.log('[ANIMATION] Osservatore impostato per rilevare il completamento dell\'animazione');
    } else {
        console.log('[ANIMATION] Elemento game-container non trovato, impossibile monitorare l\'animazione');
    }
});

// Funzione per aggiornare direttamente il rating dell'avversario nella UI
function updateOpponentRatingDisplay() {
    if (!multiplayer.opponent || !multiplayer.opponent.rating) {
        console.log('[RATING] Nessun opponent con rating disponibile');
        return;
    }

    const player2RatingElement = document.getElementById('player2-rating');
    if (player2RatingElement) {
        player2RatingElement.textContent = `(${multiplayer.opponent.rating})`;
        console.log('[RATING] Rating avversario aggiornato nella UI:', multiplayer.opponent.rating);
    } else {
        console.error('[RATING] Elemento player2-rating non trovato!');

        // Tenta con un selettore alternativo
        const alternative = document.querySelector('#player2-area .player-rating');
        if (alternative) {
            alternative.textContent = `(${multiplayer.opponent.rating})`;
            console.log('[RATING] Rating avversario aggiornato con selettore alternativo');
        }
    }
}

// Funzioni per la chat
let chatInput;
let emojiButton;
let emojiPicker;
let chatMessages;

// Inizializza gli elementi della chat
function initChat() {
    console.log('[CHAT] Inizializzazione chat multiplayer');

    // Ottieni riferimenti agli elementi
    chatInput = document.getElementById('chat-input');
    emojiButton = document.getElementById('emoji-button');
    emojiPicker = document.getElementById('emoji-picker');
    chatMessages = document.getElementById('chat-messages');

    // DEBUG: Verifica che gli elementi emoji siano stati trovati
    console.log('[EMOJI DEBUG] Elemento emoji-button trovato:', !!emojiButton);
    console.log('[EMOJI DEBUG] Elemento emoji-picker trovato:', !!emojiPicker);
    
    if (emojiButton) {
        console.log('[EMOJI DEBUG] Pulsante emoji DOM element:', emojiButton);
        console.log('[EMOJI DEBUG] Pulsante emoji classList:', emojiButton.classList.toString());
        console.log('[EMOJI DEBUG] Pulsante emoji style.display:', emojiButton.style.display);
    } else {
        console.error('[EMOJI ERROR] Pulsante emoji NON trovato nel DOM!');
    }
    
    if (emojiPicker) {
        console.log('[EMOJI DEBUG] Emoji picker DOM element:', emojiPicker);
        console.log('[EMOJI DEBUG] Emoji picker classList:', emojiPicker.classList.toString());
        console.log('[EMOJI DEBUG] Emoji picker style.display:', emojiPicker.style.display);
    } else {
        console.error('[EMOJI ERROR] Emoji picker NON trovato nel DOM!');
    }

    // La chat sarà sempre visibile nella sidebar, non nascondiamola all'inizio
    // ma aggiorniamo la sua visibilità in base allo stato del gioco
    updateChatVisibility();

    // NON registrare l'event listener qui - sarà fatto quando il socket è realmente connesso
    // Il socket potrebbe non essere ancora connesso in questo momento
    console.log('[CHAT] DEBUG: Socket presente ma potrebbe non essere ancora connesso. Socket ID:', window.socket?.id);
    console.log('[CHAT] Event listener per chatMessage sarà registrato quando il socket è connesso');

    // Inizializza gli event listener
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && chatInput.value.trim() !== '') {
                const trimmedMessage = chatInput.value.trim();
                console.log('[CHAT] Invio messaggio dalla tastiera:', trimmedMessage);
                sendChatMessage(trimmedMessage);
                chatInput.value = '';
            }
        });

        // Aggiungi anche un event listener per click su invio
        const chatForm = chatInput.closest('form');
        if (!chatForm) {
            // Se non c'è un form parent, creiamo un bottone di invio
            const sendButton = document.createElement('button');
            sendButton.type = 'button';
            sendButton.className = 'send-button';
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            sendButton.title = 'Invia messaggio';

            // Inseriamo il bottone prima dell'emoji button
            const emojiButton = document.getElementById('emoji-button');
            if (emojiButton && emojiButton.parentNode) {
                emojiButton.parentNode.insertBefore(sendButton, emojiButton);

                // Aggiungi event listener al bottone
                sendButton.addEventListener('click', function() {
                    if (chatInput.value.trim() !== '') {
                        const trimmedMessage = chatInput.value.trim();
                        console.log('[CHAT] Invio messaggio dal bottone:', trimmedMessage);
                        sendChatMessage(trimmedMessage);
                        chatInput.value = '';
                        chatInput.focus();
                    }
                });
            }
        }
    }

    if (emojiButton) {
        console.log('[EMOJI DEBUG] Aggiungendo event listener click al pulsante emoji');
        emojiButton.addEventListener('click', function(e) {
            console.log('[EMOJI DEBUG] Click rilevato su pulsante emoji');
            e.preventDefault();
            e.stopPropagation();
            toggleEmojiPicker();
        });
        console.log('[EMOJI DEBUG] Event listener click aggiunto con successo');
    } else {
        console.error('[EMOJI ERROR] Impossibile aggiungere event listener - pulsante emoji non trovato');
    }

    // Aggiungi event listener per gli emoji
    const emojiItems = document.querySelectorAll('.emoji-item');
    console.log('[EMOJI DEBUG] Trovati', emojiItems.length, 'elementi emoji');
    emojiItems.forEach((emoji, index) => {
        console.log('[EMOJI DEBUG] Aggiungendo listener a emoji', index, ':', emoji.textContent);
        emoji.addEventListener('click', function(e) {
            console.log('[EMOJI DEBUG] Click su emoji:', emoji.textContent);
            e.preventDefault();
            e.stopPropagation();
            insertEmoji(emoji.textContent);
            toggleEmojiPicker();
        });
    });

    // Chiudi emoji picker quando si clicca altrove
    document.addEventListener('click', function(e) {
        if (emojiPicker && emojiPicker.classList.contains('active') &&
            !emojiPicker.contains(e.target) &&
            e.target !== emojiButton) {
            console.log('[EMOJI DEBUG] Click fuori dal picker - chiudendo');
            emojiPicker.classList.remove('active');
        }
    });

    // Event listener già registrato sopra, rimuovo questo duplicato
    // if (window.socket) {
    //     window.socket.on('chatMessage', handleChatMessage);
    // }

    console.log('[CHAT] Chat multiplayer inizializzata');
}

// Mostra/nascondi emoji picker
function toggleEmojiPicker() {
    console.log('[EMOJI DEBUG] toggleEmojiPicker chiamata');
    console.log('[EMOJI DEBUG] emojiPicker esistente:', !!emojiPicker);
    
    if (emojiPicker) {
        const wasActive = emojiPicker.classList.contains('active');
        console.log('[EMOJI DEBUG] Stato precedente picker:', wasActive ? 'attivo' : 'nascosto');
        
        emojiPicker.classList.toggle('active');
        
        const isNowActive = emojiPicker.classList.contains('active');
        console.log('[EMOJI DEBUG] Nuovo stato picker:', isNowActive ? 'attivo' : 'nascosto');
        console.log('[EMOJI DEBUG] Classi CSS del picker:', emojiPicker.classList.toString());
        
        // Verifica anche il display CSS
        const computedStyle = window.getComputedStyle(emojiPicker);
        console.log('[EMOJI DEBUG] CSS display:', computedStyle.display);
        console.log('[EMOJI DEBUG] CSS visibility:', computedStyle.visibility);
        console.log('[EMOJI DEBUG] CSS opacity:', computedStyle.opacity);
    } else {
        console.error('[EMOJI ERROR] toggleEmojiPicker: emojiPicker è null/undefined');
    }
}

// Inserisci emoji nella casella di input
function insertEmoji(emoji) {
    console.log('[EMOJI DEBUG] insertEmoji chiamata con:', emoji);
    console.log('[EMOJI DEBUG] chatInput esistente:', !!chatInput);
    
    if (chatInput) {
        console.log('[EMOJI DEBUG] Valore attuale input:', chatInput.value);
        chatInput.value += emoji;
        console.log('[EMOJI DEBUG] Nuovo valore input:', chatInput.value);
        chatInput.focus();
        console.log('[EMOJI DEBUG] Focus impostato su input');
    } else {
        console.error('[EMOJI ERROR] insertEmoji: chatInput è null/undefined');
    }
}



// Invia messaggio chat al server
function sendChatMessage(message) {
    // Ottieni il gameId corrente in modo affidabile
    const currentGameId = multiplayer.currentGameId || 
                        (window.currentGameState && window.currentGameState.gameId) ||
                        window.currentGameId;
    
    if (!window.socket || !window.socket.connected || !currentGameId) {
        showError('Impossibile inviare il messaggio: connessione al server non disponibile');
        console.log('[CHAT] Debug invio messaggio fallito:', {
            socket: !!window.socket,
            connected: window.socket?.connected,
            currentGameId: currentGameId,
            multiplayerCurrentGameId: multiplayer.currentGameId,
            windowCurrentGameId: window.currentGameId,
            gameStateGameId: window.currentGameState?.gameId
        });
        return;
    }

    console.log('[CHAT] Invio messaggio:', message, 'gameId:', currentGameId);
    
    try {
        window.socket.emit('chatMessage', {
            gameId: currentGameId,
            message: message
        });
    } catch (error) {
        console.error('[CHAT] Errore durante invio messaggio:', error);
        showError('Errore durante l\'invio del messaggio');
        return;
    }

    // Aggiungi subito il messaggio nella chat locale (ottimistic UI)
    const userName = window.authUtils?.getCurrentUser()?.username || 'Tu';
    addMessageToChat(message, userName, true);
}

// Gestisci i messaggi ricevuti
function handleChatMessage(data) {
    console.log('[CHAT] Ricevuto messaggio chat:', data);
    console.log('[CHAT] DEBUG: data.senderId =', data.senderId);
    console.log('[CHAT] DEBUG: window.socket.id =', window.socket && window.socket.id);
    console.log('[CHAT] DEBUG: senderId !== socket.id?', data.senderId !== (window.socket && window.socket.id));

    // Se non è un messaggio inviato da noi, aggiungi alla chat
    if (data.senderId !== (window.socket && window.socket.id)) {
        // Log aggiuntivo per debug
        console.log('[CHAT] Visualizzando messaggio dall\'avversario:', data.senderName, data.message);
        addMessageToChat(data.message, data.senderName, false);

        // Riproduci un suono di notifica quando arriva un messaggio
        if (window.gameAudio && window.gameAudio.play) {
            window.gameAudio.play('card', 0.2); // Usa un suono esistente a basso volume
        }
    } else {
        console.log('[CHAT] DEBUG: Messaggio ignorato perché inviato da noi stessi');
    }
}

// Aggiungi messaggio alla chat
function addMessageToChat(message, senderName, isSelf) {
    if (!chatMessages) return;

    const messageElement = document.createElement('div');
    messageElement.className = `chat-message ${isSelf ? 'self' : 'other'}`;

    // Per i messaggi propri, colore blu
    // Per i messaggi altrui, colore verde
    const nameColor = isSelf ? '#3498db' : '#27ae60';

    const nameSpan = document.createElement('span');
    nameSpan.className = 'sender-name';
    nameSpan.textContent = senderName + ': ';
    nameSpan.style.color = nameColor;

    const messageText = document.createElement('span');
    messageText.className = 'message-text';
    messageText.textContent = message;

    messageElement.appendChild(nameSpan);
    messageElement.appendChild(messageText);

    chatMessages.appendChild(messageElement);

    // Scroll verso il basso per mostrare il nuovo messaggio con un piccolo delay
    // per assicurarsi che l'elemento sia completamente renderizzato
    requestAnimationFrame(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    });

    // Evidenzia brevemente il messaggio per attirare l'attenzione
    setTimeout(() => {
        messageElement.classList.add('highlight');
        setTimeout(() => {
            messageElement.classList.remove('highlight');
        }, 1000);
    }, 100);
}

// Mostra/nascondi chat in base allo stato multiplayer
function updateChatVisibility() {
    const chatArea = document.getElementById('chat-area');
    
    if (chatArea) {
        // La chat è sempre visibile nella sidebar quando siamo nella schermata di gioco
        chatArea.style.display = 'block';

        // Controllo multiplo per determinare se siamo in una partita multiplayer
        const isMultiplayerActive = (
            multiplayer.currentGameId || 
            (window.isGameRunning && window.currentGameState && window.currentGameState.mode === 'online') ||
            (window.currentGameState && window.currentGameState.gameId)
        );
        
        console.log('[CHAT] Controllo visibilità chat:', {
            currentGameId: multiplayer.currentGameId,
            isGameRunning: window.isGameRunning,
            gameMode: window.currentGameState?.mode,
            gameId: window.currentGameState?.gameId,
            isMultiplayerActive: isMultiplayerActive
        });

        // Se siamo in multiplayer, aggiungiamo una classe per indicarlo
        if (isMultiplayerActive) {
            chatArea.classList.add('multiplayer-active');
            console.log('[CHAT] Chat abilitata per il multiplayer');
        } else {
            chatArea.classList.remove('multiplayer-active');
            console.log('[CHAT] Chat disabilitata - non in multiplayer');
        }
    }
}

// Funzione per assicurarsi che la chat sia configurata correttamente
function ensureChatListeners() {
    if (window.socket && window.socket.connected) {
        console.log('[CHAT] DEBUG: Verifica event listener per chatMessage. Socket ID:', window.socket.id);
        const listeners = window.socket.listeners('chatMessage');
        console.log('[CHAT] DEBUG: Event listener attuali per chatMessage:', listeners.length);
        
        if (listeners.length === 0) {
            console.log('[CHAT] DEBUG: Nessun listener trovato, registrando evento chatMessage');
            window.socket.on('chatMessage', function(data) {
                console.log('[CHAT] DEBUG: Ricevuto messaggio in ensureChatListeners:', data);
                handleChatMessage(data);
            });
            console.log('[CHAT] Event listener per chatMessage registrato in ensureChatListeners');
        }
    } else {
        console.log('[CHAT] DEBUG: Socket non connesso, impossibile registrare listener');
    }
}

// NOTA: Questo DOMContentLoaded listener è stato unito con quello principale più in alto

// Export functions for use in other scripts
window.multiplayer = {
    startMatchmaking,
    cancelMatchmaking,
    createPrivateGame,
    joinPrivateGame,
    executeGameAction,
    updateGameStateWithTurnTimer,
    showError,
    showNotification,
    updateOpponentRatingDisplay,
    startTurnTimer,
    stopTurnTimer,
    // handleGameState è gestito in script.js
    // Chat functions
    sendChatMessage,
    handleChatMessage,
    ensureChatListeners,
    updateChatVisibility,  // Aggiunta per permettere l'accesso esterno
    // Esponi anche queste variabili per debugging
    state: multiplayer
};

// Rendi createHandSlots disponibile globalmente per permettere ad altri script di utilizzarla
window.createHandSlots = createHandSlots;
