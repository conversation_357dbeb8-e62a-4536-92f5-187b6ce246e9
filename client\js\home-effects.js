/**
 * home-effects.js
 * Script per effetti visivi e animazioni nella pagina home di Skèmino
 */

// Esempio di logica per simulare cambio vista utente loggato/non loggato
function toggleUserView() {
    const guestView = document.getElementById('guest-title-view');
    const loggedView = document.getElementById('logged-user-title-view');
    
    if (guestView.style.display === 'none') {
        guestView.style.display = 'block';
        loggedView.style.display = 'none';
    } else {
        guestView.style.display = 'none';
        loggedView.style.display = 'block';
    }
}

// Toggle auth sections
function toggleAuth() {
    const authButtons = document.getElementById('auth-buttons');
    const userProfile = document.getElementById('user-profile');
    
    if (authButtons.style.display === 'none') {
        authButtons.style.display = 'block';
        userProfile.style.display = 'none';
    } else {
        authButtons.style.display = 'none';
        userProfile.style.display = 'block';
    }
}

// Funzione per verificare lo stato di login
function checkLoginStatus() {
    // Se siamo su index.html, controlla se l'utente è loggato
    const currentPath = window.location.pathname;
    const isIndexPage = currentPath === '/' || currentPath.endsWith('index.html');
    
    if (isIndexPage) {
        // CONTROLLO DI SICUREZZA: Se l'utente è loggato su index.html, reindirizza immediatamente
        if (window.authUtils && window.authUtils.isLoggedIn && window.authUtils.isLoggedIn()) {
            console.log('[SECURITY CHECK] Utente loggato rilevato su index.html, reindirizzo immediato a /home');
            window.location.href = '/home';
            return;
        }
        
        // Fallback: controllo localStorage
        const loggedInUser = localStorage.getItem('loggedInUser');
        const token = localStorage.getItem('token');
        if (loggedInUser && token) {
            console.log('[SECURITY CHECK] Utente loggato (localStorage) rilevato su index.html, reindirizzo a /home');
            window.location.href = '/home';
            return;
        }
        
        // Su index.html, mostra sempre la vista per non loggati
        const guestView = document.getElementById('guest-title-view');
        const loggedView = document.getElementById('logged-user-title-view');
        if (guestView && loggedView) {
            guestView.style.display = 'block';
            loggedView.style.display = 'none';
        }
        return false;
    }
    
    const userDataString = localStorage.getItem('user');
    const token = localStorage.getItem('token');
    
    if (userDataString && token) {
        try {
            const userData = JSON.parse(userDataString);
            // Mostra l'interfaccia per utente loggato
            showLoggedInInterface(userData);
            return true;
        } catch (error) {
            console.error('Errore nel parsing dei dati utente:', error);
            return false;
        }
    }
    return false;
}

// Funzione per mostrare l'interfaccia utente loggato
function showLoggedInInterface(userData) {
    // Mostra la vista utente loggato nella hero section
    const guestView = document.getElementById('guest-title-view');
    const loggedView = document.getElementById('logged-user-title-view');
    if (guestView && loggedView) {
        guestView.style.display = 'none';
        loggedView.style.display = 'block';
        
        // Aggiorna il nome utente nella hero
        const heroUsername = document.getElementById('hero-username');
        if (heroUsername) {
            heroUsername.textContent = userData.username || 'Giocatore';
        }
        
        // Aggiorna il rank
        const heroRank = document.getElementById('hero-rank');
        if (heroRank) {
            const rank = getRankFromRating(userData.rank || 1000);
            heroRank.textContent = rank.title;
            heroRank.className = `rank-${rank.className}`;
        }
    }
    
    // Mostra il profilo utente nella sidebar
    const authButtons = document.getElementById('auth-buttons');
    const userProfile = document.getElementById('user-profile');
    if (authButtons && userProfile) {
        authButtons.style.display = 'none';
        userProfile.style.display = 'block';
        
        // Aggiorna il nome utente nella sidebar
        const usernameDisplay = document.getElementById('username-display');
        if (usernameDisplay) {
            usernameDisplay.textContent = userData.username || 'Giocatore';
        }
        
        // Aggiorna il rank nella sidebar
        const userRank = document.getElementById('user-rank');
        if (userRank) {
            const rank = getRankFromRating(userData.rank || 1000);
            userRank.textContent = rank.title;
        }
    }
    
    // Mostra il profilo anche nella versione mobile
    const mobileAuthButtons = document.getElementById('mobile-auth-buttons');
    const mobileUserProfile = document.getElementById('mobile-user-profile');
    if (mobileAuthButtons && mobileUserProfile) {
        mobileAuthButtons.style.display = 'none';
        mobileUserProfile.style.display = 'block';
        
        const mobileUsernameDisplay = document.getElementById('mobile-username-display');
        if (mobileUsernameDisplay) {
            mobileUsernameDisplay.textContent = userData.username || 'Giocatore';
        }
        
        const mobileUserRank = document.getElementById('mobile-user-rank');
        if (mobileUserRank) {
            const rank = getRankFromRating(userData.rank || 1000);
            mobileUserRank.textContent = rank.title;
        }
    }
}

// Funzione helper per ottenere il rank dal rating
function getRankFromRating(rating) {
    if (rating >= 2700) return { title: 'Super Gran Maestro', className: 'super' };
    if (rating >= 2500) return { title: 'Gran Maestro', className: 'gran-maestro' };
    if (rating >= 2400) return { title: 'Maestro Internazionale', className: 'internazionale' };
    if (rating >= 2200) return { title: 'Maestro', className: 'maestro' };
    if (rating >= 2000) return { title: 'Candidato Maestro', className: 'candidato' };
    if (rating >= 1800) return { title: 'Dilettante A', className: 'dilettante-a' };
    if (rating >= 1600) return { title: 'Dilettante B', className: 'dilettante-b' };
    if (rating >= 1400) return { title: 'Dilettante C', className: 'dilettante-c' };
    if (rating >= 1200) return { title: 'Dilettante D', className: 'dilettante-d' };
    return { title: 'Principiante', className: 'principiante' };
}

// Attendi che il DOM sia completamente caricato
document.addEventListener('DOMContentLoaded', function() {
    // Aggiungi classe loaded al body per evitare flicker
    setTimeout(() => {
        document.body.classList.add('loaded');
        console.log('[FADEIN] Pagina caricata, animazioni attivate');
    }, 100);
    
    // Se siamo su index.html e l'utente è loggato, lascia che redirect.js gestisca il redirect
    const currentPath = window.location.pathname;
    const isIndexPage = currentPath === '/' || currentPath.endsWith('index.html');
    
    if (isIndexPage && window.authUtils && window.authUtils.isLoggedIn && window.authUtils.isLoggedIn()) {
        // Non fare nulla qui, lascia che redirect.js gestisca il redirect
        return;
    }
    
    // Verifica immediatamente lo stato di login
    checkLoginStatus();
    
    // Effetto parallax sul mouse
    document.addEventListener('mousemove', (e) => {
        const cards = document.querySelectorAll('.skemino-feature-card');
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;
        
        cards.forEach((card, index) => {
            const depth = index * 0.1;
            const moveX = (x - 0.5) * depth * 10;
            const moveY = (y - 0.5) * depth * 10;
            card.style.transform = `translateY(-10px) translateX(${moveX}px) translateY(${moveY}px)`;
        });
    });

    // Animazioni Fadein per le sezioni principali
    const fadeInObserverOptions = {
        threshold: 0.15,
        rootMargin: '0px 0px -100px 0px'
    };

    const fadeInObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('fade-in')) {
                entry.target.classList.add('fade-in');
                console.log(`[FADEIN] Sezione ${entry.target.className} animata`);
            }
        });
    }, fadeInObserverOptions);

    // Osserva le sezioni principali per l'animazione fadein
    document.querySelectorAll('.skemino-hero-section, .skemino-rules-section, .skemino-academy-section').forEach(section => {
        fadeInObserver.observe(section);
    });

    // Mostra immediatamente la hero section se è già visibile al caricamento
    const heroSection = document.querySelector('.skemino-hero-section');
    if (heroSection) {
        const rect = heroSection.getBoundingClientRect();
        if (rect.top < window.innerHeight && rect.bottom > 0) {
            setTimeout(() => {
                heroSection.classList.add('fade-in');
                console.log('[FADEIN] Hero section mostrata immediatamente');
            }, 200);
        }
    }

    // Animazione scroll reveal per elementi interni
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, observerOptions);

    document.querySelectorAll('.skemino-section-header, .skemino-feature-card').forEach(el => {
        el.style.animationPlayState = 'paused';
        observer.observe(el);
    });

    // Uncomment per testare
    // toggleUserView();
    // toggleAuth();
});

// Esporta funzioni per l'uso in altri script
window.skeminoHomeEffects = {
    toggleUserView: toggleUserView,
    toggleAuth: toggleAuth
};