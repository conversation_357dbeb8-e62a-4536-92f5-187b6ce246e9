// Controllo autenticazione per game.html
// Mostra il popup di registrazione solo se l'utente non è autenticato

let authCheckAttempts = 0;
const MAX_AUTH_CHECK_ATTEMPTS = 10;
const AUTH_CHECK_DELAY = 200;

window.addEventListener('DOMContentLoaded', () => {
    console.log('[AUTH-CHECK] DOM caricato, avvio controllo autenticazione...');
    checkAuthenticationStatus();
});

function checkAuthenticationStatus() {
    authCheckAttempts++;
    console.log(`[AUTH-CHECK] Tentativo di controllo autenticazione ${authCheckAttempts}/${MAX_AUTH_CHECK_ATTEMPTS}`);
    
    // Prima verifica se il sistema di autenticazione è caricato
    if (window.authUtils && typeof window.authUtils.isLoggedIn === 'function') {
        console.log('[AUTH-CHECK] Sistema authUtils disponibile, controllo stato...');
        
        const isLoggedIn = window.authUtils.isLoggedIn();
        console.log('[AUTH-CHECK] Stato autenticazione authUtils:', isLoggedIn);
        
        if (isLoggedIn) {
            console.log('[AUTH-CHECK] Utente autenticato tramite authUtils');
            showGameOptionsContent();
            return;
        } else {
            console.log('[AUTH-CHECK] Utente NON autenticato tramite authUtils');
            showAuthRequiredContent();
            setTimeout(() => {
                showRegisterPopup();
            }, 100);
            return;
        }
    }
    
    // Fallback: controllo localStorage se authUtils non è ancora disponibile
    const loggedInUser = localStorage.getItem('loggedInUser');
    const token = localStorage.getItem('token');
    
    console.log('[AUTH-CHECK] AuthUtils non disponibile, controllo localStorage...');
    console.log('[AUTH-CHECK] loggedInUser:', loggedInUser ? 'presente' : 'assente');
    console.log('[AUTH-CHECK] token:', token ? 'presente' : 'assente');
    
    // Se i dati di base ci sono, considera l'utente autenticato
    if (loggedInUser && token) {
        try {
            const userData = JSON.parse(loggedInUser);
            if (userData && userData.username) {
                console.log('[AUTH-CHECK] Dati utente validi nel localStorage, utente considerato autenticato');
                showGameOptionsContent();
                return;
            }
        } catch (e) {
            console.log('[AUTH-CHECK] Errore parsing dati utente:', e);
        }
    }
    
    // Se non abbiamo ancora dati validi e non abbiamo raggiunto il limite di tentativi
    if (authCheckAttempts < MAX_AUTH_CHECK_ATTEMPTS) {
        console.log(`[AUTH-CHECK] Dati non ancora disponibili, riprovo tra ${AUTH_CHECK_DELAY}ms...`);
        setTimeout(checkAuthenticationStatus, AUTH_CHECK_DELAY);
        return;
    }
    
    // Se abbiamo raggiunto il limite di tentativi senza trovare autenticazione
    console.log('[AUTH-CHECK] Limite tentativi raggiunto, utente considerato NON autenticato');
    showAuthRequiredContent();
    setTimeout(() => {
        showRegisterPopup();
    }, 100);
}

function showLoginPopup() {
    console.log('[AUTH-CHECK] showLoginPopup() chiamata');
    
    const loginPopup = document.getElementById('login-popup');
    const registerPopup = document.getElementById('register-popup');
    
    console.log('[AUTH-CHECK] loginPopup:', loginPopup ? 'trovato' : 'NON TROVATO');
    console.log('[AUTH-CHECK] registerPopup:', registerPopup ? 'trovato' : 'NON TROVATO');
    
    if (!loginPopup) return;
    
    // Reset completo di tutti i popup
    if (registerPopup) {
        registerPopup.style.display = 'none';
        registerPopup.classList.remove('show', 'bounce', 'fadein');
    }
    
    // Reset iniziale login popup
    loginPopup.style.display = 'none';
    loginPopup.classList.remove('show', 'bounce', 'fadein');
    
    // Mostra il popup con animazione fadeIn
    setTimeout(() => {
        loginPopup.style.display = 'flex';
        loginPopup.classList.add('show');
        console.log('[AUTH-CHECK] Login popup - aggiunta classe "show"');
        
        // Aggiungi l'effetto fadeIn dopo un breve ritardo
        setTimeout(() => {
            loginPopup.classList.add('fadein');
            console.log('[AUTH-CHECK] Login popup - aggiunta classe "fadein"');
        }, 50);
    }, 100);
    
    // Gestisci la chiusura del popup
    const closeButton = loginPopup.querySelector('.close-modal');
            if (closeButton) {
            closeButton.addEventListener('click', () => {
                loginPopup.classList.remove('show', 'bounce', 'fadein');
            });
        }
        
        // Chiudi il popup cliccando fuori
        loginPopup.addEventListener('click', (e) => {
            if (e.target === loginPopup) {
                loginPopup.classList.remove('show', 'bounce', 'fadein');
            }
        });
    
    // Gestisci il click sul pulsante di login
    const loginButton = document.getElementById('popup-login-button');
    if (loginButton) {
        loginButton.addEventListener('click', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('popup-login-username').value;
            const password = document.getElementById('popup-login-password').value;
            const rememberMe = document.getElementById('popup-remember-me')?.checked || false;
            const errorMessage = document.getElementById('login-error-message');
            
            if (!username || !password) {
                if (errorMessage) {
                    errorMessage.textContent = 'Inserisci username e password.';
                }
                return;
            }
            
            try {
                // Disabilita il pulsante durante il login
                loginButton.disabled = true;
                loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Accesso in corso...';
                
                // Esegui il login
                if (window.authUtils && window.authUtils.login) {
                    await window.authUtils.login(username, password, rememberMe);
                    
                    // Login riuscito, chiudi il popup e reindirizza alla home-logged
                    loginPopup.classList.remove('show', 'bounce', 'fadein');
                    
                    // Opzionale: mostra un messaggio di benvenuto
                    if (window.showGameMessage) {
                        window.showGameMessage('Login effettuato con successo! Reindirizzamento...', 2000, '#4CAF50');
                    }
                    
                    // Reindirizza alla home dei loggati
                    setTimeout(() => {
                        window.location.href = 'home-logged.html';
                    }, 1000);
                }
            } catch (error) {
                // Login fallito
                if (errorMessage) {
                    errorMessage.textContent = error.message || 'Login fallito. Verifica username e password.';
                }
                
                // Riabilita il pulsante
                loginButton.disabled = false;
                loginButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> Accedi';
            }
        });
    }
    
    // Gestisci il click sul link "Registrati"
    const switchToRegisterButton = document.getElementById('popup-switch-to-register');
    if (switchToRegisterButton) {
        switchToRegisterButton.addEventListener('click', (e) => {
            e.preventDefault();
            showRegisterPopup();
        });
    }
}

function showRegisterPopup() {
    console.log('[AUTH-CHECK] showRegisterPopup() chiamata');
    
    const loginPopup = document.getElementById('login-popup');
    const registerPopup = document.getElementById('register-popup');
    
    console.log('[AUTH-CHECK] loginPopup:', loginPopup ? 'trovato' : 'NON TROVATO');
    console.log('[AUTH-CHECK] registerPopup:', registerPopup ? 'trovato' : 'NON TROVATO');
    
    // Reset completo di tutti i popup prima di mostrare quello giusto
    if (loginPopup) {
        loginPopup.style.display = 'none';
        loginPopup.classList.remove('show', 'bounce', 'fadein');
    }
    
    if (registerPopup) {
        console.log('[AUTH-CHECK] Mostro popup registrazione...');
        // Reset iniziale
        registerPopup.style.display = 'none';
        registerPopup.classList.remove('show', 'bounce', 'fadein');
        
        // Mostra il popup con animazione fadeIn
        setTimeout(() => {
            registerPopup.style.display = 'flex';
            registerPopup.classList.add('show');
            console.log('[AUTH-CHECK] Aggiunta classe "show"');
            setTimeout(() => {
                registerPopup.classList.add('fadein');
                console.log('[AUTH-CHECK] Aggiunta classe "fadein"');
            }, 50);
        }, 100);
        
        // Gestisci la chiusura del popup di registrazione
        const closeButton = registerPopup.querySelector('.close-modal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                registerPopup.classList.remove('show', 'bounce', 'fadein');
            });
        }
        
        // Chiudi il popup cliccando fuori
        registerPopup.addEventListener('click', (e) => {
            if (e.target === registerPopup) {
                registerPopup.classList.remove('show', 'bounce', 'fadein');
            }
        });
        
        // Gestisci il click sul link "Accedi"
        const switchToLoginButton = document.getElementById('popup-switch-to-login');
        if (switchToLoginButton) {
            switchToLoginButton.addEventListener('click', (e) => {
                e.preventDefault();
                registerPopup.classList.remove('show', 'bounce', 'fadein');
                setTimeout(() => {
                    showLoginPopup();
                }, 200);
            });
        }
        
        // Gestisci il click sul pulsante di registrazione
        const registerButton = document.getElementById('popup-register-button');
        if (registerButton) {
            registerButton.addEventListener('click', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('popup-register-username').value;
                const email = document.getElementById('popup-register-email').value;
                const password = document.getElementById('popup-register-password').value;
                const confirmPassword = document.getElementById('popup-register-confirm-password').value;
                const termsAgreed = document.getElementById('popup-terms-agreement')?.checked || false;
                const errorMessage = document.getElementById('register-error-message');
                
                if (!username || !email || !password || !confirmPassword) {
                    if (errorMessage) {
                        errorMessage.textContent = 'Compila tutti i campi.';
                    }
                    return;
                }
                
                if (password !== confirmPassword) {
                    if (errorMessage) {
                        errorMessage.textContent = 'Le password non coincidono.';
                    }
                    return;
                }
                
                if (!termsAgreed) {
                    if (errorMessage) {
                        errorMessage.textContent = 'Devi accettare i termini e le condizioni.';
                    }
                    return;
                }
                
                try {
                    // Disabilita il pulsante durante la registrazione
                    registerButton.disabled = true;
                    registerButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Registrazione in corso...';
                    
                    // Esegui la registrazione
                    if (window.authUtils && window.authUtils.register) {
                        await window.authUtils.register(username, email, password);
                        
                        // Registrazione riuscita, chiudi il popup e reindirizza alla home-logged
                        registerPopup.classList.remove('show', 'bounce', 'fadein');
                        
                        // Mostra un messaggio di successo
                        if (window.showGameMessage) {
                            window.showGameMessage('Registrazione effettuata con successo! Reindirizzamento...', 2000, '#4CAF50');
                        }
                        
                        // Reindirizza alla home dei loggati
                        setTimeout(() => {
                            window.location.href = 'home-logged.html';
                        }, 1000);
                    }
                } catch (error) {
                    // Registrazione fallita
                    if (errorMessage) {
                        errorMessage.textContent = error.message || 'Registrazione fallita. Riprova.';
                    }
                    
                    // Riabilita il pulsante
                    registerButton.disabled = false;
                    registerButton.innerHTML = '<i class="fas fa-user-plus"></i> Registrati';
                }
            });
        }
    }
}

// Funzioni per gestire la visualizzazione del contenuto nel tab "Nuova Partita"
function showAuthRequiredContent() {
    console.log('[AUTH-CHECK] Mostro contenuto autenticazione richiesta nel tab');
    
    const authContent = document.getElementById('auth-required-content');
    const gameContent = document.getElementById('game-options-content');
    
    if (authContent) {
        authContent.style.display = 'block';
        console.log('[AUTH-CHECK] Contenuto auth mostrato');
    }
    
    if (gameContent) {
        gameContent.style.display = 'none';
        console.log('[AUTH-CHECK] Contenuto game nascosto');
    }
}

function showGameOptionsContent() {
    console.log('[AUTH-CHECK] Mostro contenuto opzioni di gioco nel tab');
    
    const authContent = document.getElementById('auth-required-content');
    const gameContent = document.getElementById('game-options-content');
    
    if (authContent) {
        authContent.style.display = 'none';
        console.log('[AUTH-CHECK] Contenuto auth nascosto');
    }
    
    if (gameContent) {
        gameContent.style.display = 'block';
        console.log('[AUTH-CHECK] Contenuto game mostrato');
    }
} 