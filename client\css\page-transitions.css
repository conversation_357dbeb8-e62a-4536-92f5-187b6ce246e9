/* Stili per eliminare i glitch durante la transizione tra pagine */
.page-transition {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 9999;
    background-color: #172a45;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0s 0.3s;
    pointer-events: none;
}

.page-transition.active {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease;
    pointer-events: all;
}

/* Rimosso miglioramento animazioni popup che causava conflitti */

/* Preload di elementi comuni */
body::after {
    content: '';
    display: none;
    background-image: 
        url('../img/carte/skemino.webp'),
        url('../img/carte/card-back.webp'),
        url('../img/Cover carte/cover.png');
}

/* Evita flash FOUC */
body {
    visibility: hidden;
}

/* Eccezione per homelogged - mostra immediatamente per evitare schermata nera */
body:has(.skemino-main_wrapper),
body:has(.skemino-sidebar),
body:has(.skemino-page-wrapper) {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Disabilita le transizioni per homelogged */
body:has(.skemino-main_wrapper) .page-transition,
body:has(.skemino-sidebar) .page-transition,
body:has(.skemino-page-wrapper) .page-transition {
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

body.loaded {
    visibility: visible;
    animation: fadein 0.3s ease-in-out;
}

@keyframes fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* === NUOVE ANIMAZIONI FLUIDE PER GAME.HTML === */

/* Animazioni fluide per le aree giocatori durante il caricamento iniziale - SOLO DISSOLVENZA */
body.game-page-loading #players-column {
    opacity: 0 !important;
    transition: opacity 0.8s ease-out !important;
}

body.game-page-loading #player1-area,
body.game-page-loading #player2-area {
    opacity: 0 !important;
    transition: opacity 0.6s ease-out !important;
}

/* Includi anche la board-sidebar per dissolvenza fluida */
body.game-page-loading #board-sidebar {
    opacity: 0 !important;
    transition: opacity 0.7s ease-out !important;
}

/* Includi il game-board per apparire per ultimo */
body.game-page-loading #game-board {
    opacity: 0 !important;
    transition: opacity 0.8s ease-out !important;
}

/* Includi anche gli slot delle carte per evitare glitch */
body.game-page-loading #player1-hand,
body.game-page-loading #player2-hand,
body.game-page-loading .hand-area {
    opacity: 0 !important;
    transition: opacity 0.5s ease-out !important;
}

body.game-page-loading #player1-area .player-info-box,
body.game-page-loading #player2-area .player-info-box {
    opacity: 0 !important;
    transition: opacity 0.4s ease-out !important;
}

body.game-page-loading #player1-area .player-name,
body.game-page-loading #player2-area .player-name {
    opacity: 0 !important;
    transition: opacity 0.3s ease-out !important;
}

/* Stati visibili con dissolvenza progressiva */
body.game-page-loading #players-column.show-animated {
    opacity: 1 !important;
}

body.game-page-loading #player1-area.show-animated {
    opacity: 1 !important;
    transition-delay: 0.2s !important;
}

body.game-page-loading #player2-area.show-animated {
    opacity: 1 !important;
    transition-delay: 0.4s !important;
}

/* Board sidebar con dissolvenza coordinata */
body.game-page-loading #board-sidebar.show-animated {
    opacity: 1 !important;
    transition-delay: 0.5s !important;
}

/* Slot delle carte con dissolvenza ritardata */
body.game-page-loading #player1-hand.show-animated,
body.game-page-loading #player1-area.show-animated .hand-area {
    opacity: 1 !important;
    transition-delay: 0.6s !important;
}

body.game-page-loading #player2-hand.show-animated,
body.game-page-loading #player2-area.show-animated .hand-area {
    opacity: 1 !important;
    transition-delay: 0.8s !important;
}

body.game-page-loading #player1-area.show-animated .player-info-box,
body.game-page-loading #player2-area.show-animated .player-info-box {
    opacity: 1 !important;
    transition-delay: 0.7s !important;
}

body.game-page-loading #player1-area.show-animated .player-name,
body.game-page-loading #player2-area.show-animated .player-name {
    opacity: 1 !important;
    transition-delay: 0.9s !important;
}

/* Game board appare per ultimo */
body.game-page-loading #game-board.show-animated {
    opacity: 1 !important;
    transition-delay: 1.0s !important;
}

/* Stato consistente durante le transizioni */

/* Animazioni fluide per players-column */
#players-column {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* Stato visibile dopo caricamento */
#players-column.loaded {
    opacity: 1;
    transform: translateX(0);
}

/* Animazione ritardata per player areas individuali */
#player1-area,
#player2-area {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

#player1-area.loaded {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.2s;
}

#player2-area.loaded {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.4s;
}

/* Animazione per player-info-box per evitare glitch */
.player-info-box {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.player-info-box.loaded {
    opacity: 1;
    transform: scale(1);
    transition-delay: 0.6s;
}

/* Animazione per nomi giocatori - solo se non già visibili */
.game-loading .player-name {
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.player-name.loaded {
    opacity: 1 !important;
    transition-delay: 0.8s;
}

/* Stato di caricamento per evitare flash */
.game-loading #players-column,
.game-loading #player1-area,
.game-loading #player2-area,
.game-loading .player-info-box,
.game-loading .player-name {
    opacity: 0;
    transform: translateX(-30px);
}

/* Prevenzione glitch durante matchmaking */
.matchmaking-active #players-column {
    opacity: 1;
    transform: translateX(0);
    transition: none;
}

.matchmaking-active #player1-area,
.matchmaking-active #player2-area,
.matchmaking-active .player-info-box,
.matchmaking-active .player-name {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: none;
}

#homepage, #game-container {
    transition: opacity 0.2s ease;
    will-change: opacity;
}

.transition-out {
    opacity: 0;
}

.transition-in {
    opacity: 1;
}

/* Previene i salti durante il caricamento di elementi comuni */
.logo-image, .card-back-image {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000;
    -webkit-perspective: 1000;
}

/* Stile comune per tutti i loghi cliccabili */
.logo-image.home-link {
    cursor: pointer;
    transition: all 0.3s ease;
}

.logo-image.home-link:hover {
    opacity: 0.9;
    transform: translateZ(0) scale(1.05);
}

/* Impedisce la sovrapposizione temporanea di elementi */
.sidebar {
    z-index: 100;
}

.preload-card-backs {
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
}