// Script per gestire la visualizzazione della hero section personalizzata per utenti loggati
document.addEventListener('DOMContentLoaded', function() {
    checkUserLoginStatus();
    setupSocialLoginButtons();
});

function checkUserLoginStatus() {
    // Verifica se l'utente è loggato usando la funzione isLoggedIn da auth.js
    if (window.authUtils && window.authUtils.isLoggedIn) {
        const isLogged = window.authUtils.isLoggedIn();
        
        if (isLogged) {
            // L'utente è loggato, ottiene i suoi dati
            const user = window.authUtils.getCurrentUser();
            if (user) {
                // Mostra la vista per l'utente loggato
                showLoggedUserHero(user);
            }
        } else {
            // Nascondi le feature-box per utenti non loggati
            const featureBoxes = document.querySelector('.feature-boxes');
            if (featureBoxes) {
                featureBoxes.style.display = 'none';
            }
            
            // Nascondi solo il pulsante "Gioca a 30 minuti" per utenti non loggati
            const playOnlineButton = document.getElementById('play-online-button');
            // const trainingButton = document.getElementById('training-button');
            
            if (playOnlineButton) {
                playOnlineButton.style.display = 'none';
            }
            
            // Commento rimosso - il pulsante Allenamento sarà sempre visibile
            // if (trainingButton) {
            //     trainingButton.style.display = 'none';
            // }
        }
    } else {
        // Fallback nel caso in cui authUtils non sia ancora disponibile
        setTimeout(checkUserLoginStatus, 100);
    }
}

function showLoggedUserHero(user) {
    // Nascondi la vista per gli ospiti
    document.getElementById('guest-title-view').style.display = 'none';
    
    // Mostra la vista per gli utenti loggati
    document.getElementById('logged-user-title-view').style.display = 'block';
    
    // Nascondi l'intero board-preview nella hero section per utenti loggati
    const boardPreview = document.querySelector('.board-preview');
    if (boardPreview) {
        boardPreview.style.display = 'none';
    }
    
    // Mostra le feature boxes per utenti loggati
    const featureBoxes = document.querySelector('.feature-boxes');
    if (featureBoxes) {
        featureBoxes.style.display = 'flex';
    }
    
    // Imposta il nome utente
    document.getElementById('hero-username').textContent = user.username || 'Giocatore';
    
    // Calcola e imposta il livello/rank in base al rating
    const rating = user.rating || 1000;
    const rankElement = document.getElementById('hero-rank');
    
    let rankName = 'Principiante';
    let rankClass = 'rank-principiante';
    
    if (rating >= 2700) {
        rankName = 'Super Gran Maestro';
        rankClass = 'rank-super';
    } else if (rating >= 2500) {
        rankName = 'Gran Maestro';
        rankClass = 'rank-gran-maestro';
    } else if (rating >= 2400) {
        rankName = 'Maestro Internazionale';
        rankClass = 'rank-internazionale';
    } else if (rating >= 2200) {
        rankName = 'Maestro';
        rankClass = 'rank-maestro';
    } else if (rating >= 2000) {
        rankName = 'Candidato Maestro';
        rankClass = 'rank-candidato';
    } else if (rating >= 1800) {
        rankName = 'Dilettante A';
        rankClass = 'rank-dilettante-a';
    } else if (rating >= 1600) {
        rankName = 'Dilettante B';
        rankClass = 'rank-dilettante-b';
    } else if (rating >= 1400) {
        rankName = 'Dilettante C';
        rankClass = 'rank-dilettante-c';
    } else if (rating >= 1200) {
        rankName = 'Dilettante D';
        rankClass = 'rank-dilettante-d';
    }
    
    rankElement.textContent = rankName;
    rankElement.className = rankClass;
    
    // Imposta la bandiera della nazionalità dell'utente (default: Italia)
    const flagElement = document.getElementById('hero-flag');
    const country = user.country || 'it';
    flagElement.className = 'country-flag flag-' + country;
    
    // Carica l'avatar dell'utente
    loadUserAvatar(user);
}

function loadUserAvatar(user) {
    const avatarContainer = document.querySelector('.user-hero-avatar');
    if (!avatarContainer) return;
    
    // Calcola il livello in base al rating
    const rating = user.rating || 1000;
    let avatarLevel = 'Principiante';
    
    if (rating >= 2700) {
        avatarLevel = 'Super Gran Maestro';
    } else if (rating >= 2500) {
        avatarLevel = 'Gran Maestro';
    } else if (rating >= 2400) {
        avatarLevel = 'Maestro Internazionale';
    } else if (rating >= 2200) {
        avatarLevel = 'Maestro';
    } else if (rating >= 2000) {
        avatarLevel = 'Candidato Maestro';
    } else if (rating >= 1800) {
        avatarLevel = 'Dilettante A';
    } else if (rating >= 1600) {
        avatarLevel = 'Dilettante B';
    } else if (rating >= 1400) {
        avatarLevel = 'Dilettante C';
    } else if (rating >= 1200) {
        avatarLevel = 'Dilettante D';
    }
    
    // Crea l'immagine dell'avatar
    avatarContainer.innerHTML = '';
    
    const avatar = document.createElement('img');
    avatar.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.webp`;
    avatar.alt = `Avatar ${avatarLevel}`;
    avatar.className = 'hero-avatar-img';
    avatar.onerror = function() {
        // Fallback se l'immagine webp non è supportata
        this.src = `img/avatar/${avatarLevel.replace(' ', '%20')}.png`;
    };
    
    avatarContainer.appendChild(avatar);
}

// Funzione per configurare i pulsanti di autenticazione social
function setupSocialLoginButtons() {
    // Pulsante Google per il login
    const googleLoginBtn = document.getElementById('google-login-btn');
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', function() {
            // Apri la finestra di popup per l'autenticazione con Google
            openGoogleAuthWindow('login');
        });
    }
    
    // Pulsante Google per la registrazione
    const googleRegisterBtn = document.getElementById('google-register-btn');
    if (googleRegisterBtn) {
        googleRegisterBtn.addEventListener('click', function() {
            // Apri la finestra di popup per l'autenticazione con Google
            openGoogleAuthWindow('register');
        });
    }
}

// Funzione per aprire la finestra di popup per l'autenticazione Google
function openGoogleAuthWindow(mode) {
    // Determina l'URL corretto in base alla modalità (login o register)
    const authUrl = `/api/auth/google?mode=${mode}`;
    
    // Apri la finestra di popup
    const popup = window.open(authUrl, 'googleAuth', 'width=500,height=600,resizable=yes');
    
    // Gestisci la comunicazione con la finestra di popup tramite messaggi
    window.addEventListener('message', function(event) {
        // Verifica che il messaggio provenga dal dominio corretto
        if (event.origin !== window.location.origin) return;
        
        // Gestisci il risultato dell'autenticazione
        if (event.data.type === 'auth_success') {
            // L'autenticazione è avvenuta con successo
            console.log('Autenticazione con Google completata con successo!');
            
            // Salva i dati dell'utente
            if (event.data.user) {
                localStorage.setItem('user', JSON.stringify(event.data.user));
                localStorage.setItem('loggedInUser', event.data.user.username || 'true');
            }
            
            // Salva il token
            if (event.data.token) {
                localStorage.setItem('token', event.data.token);
            }
            
            // Chiudi il popup
            if (popup && !popup.closed) {
                popup.close();
            }
            
            // Reinitialize socket connection with the new token
            if (window.reinitializeSocket && typeof window.reinitializeSocket === 'function') {
                window.reinitializeSocket();
            }
            
            // Aggiorna la UI o reindirizza l'utente
            if (mode === 'login') {
                // Reindirizza alla home per utenti loggati
                window.location.href = '/home';
            } else {
                // Mostra un messaggio di successo per la registrazione
                const messageElement = document.createElement('div');
                messageElement.className = 'popup-success-message';
                messageElement.innerHTML = `
                    <i class="fas fa-check-circle"></i>
                    <p>Registrazione con Google completata con successo!</p>
                `;
                
                // Aggiungi lo stile al messaggio
                messageElement.style.position = 'fixed';
                messageElement.style.top = '20px';
                messageElement.style.left = '50%';
                messageElement.style.transform = 'translateX(-50%)';
                messageElement.style.backgroundColor = '#4CAF50';
                messageElement.style.color = 'white';
                messageElement.style.padding = '15px 20px';
                messageElement.style.borderRadius = '5px';
                messageElement.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                messageElement.style.zIndex = '1000';
                messageElement.style.display = 'flex';
                messageElement.style.alignItems = 'center';
                messageElement.style.gap = '10px';
                
                document.body.appendChild(messageElement);
                
                // Rimuovi il messaggio dopo alcuni secondi
                setTimeout(() => {
                    messageElement.style.opacity = '0';
                    messageElement.style.transition = 'opacity 0.5s ease';
                    
                    setTimeout(() => {
                        document.body.removeChild(messageElement);
                        
                        // Reindirizza alla home per utenti loggati
                        window.location.href = '/home';
                    }, 500);
                }, 3000);
            }
        } else if (event.data.type === 'auth_error') {
            // L'autenticazione ha fallito
            console.error('Errore nell\'autenticazione con Google:', event.data.error);
            
            // Chiudi il popup
            if (popup && !popup.closed) {
                popup.close();
            }
            
            // Mostra un messaggio di errore
            alert('Errore durante l\'autenticazione con Google: ' + (event.data.error || 'Si è verificato un errore imprevisto'));
        }
    });
}