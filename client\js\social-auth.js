/**
 * Social Authentication Helpers
 * Gestisce l'autenticazione tramite provider social come Google
 */

document.addEventListener('DOMContentLoaded', function() {
    // Configura i pulsanti di autenticazione social se esistono
    setupSocialLoginButtons();
});

/**
 * Configura i pulsanti di autenticazione social
 */
function setupSocialLoginButtons() {
    // Pulsante Google per il login
    const googleLoginBtn = document.getElementById('google-login-btn');
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', function() {
            // Apri la finestra di popup per l'autenticazione con Google
            openGoogleAuthWindow('login');
        });
    }
    
    // Pulsante Google per la registrazione
    const googleRegisterBtn = document.getElementById('google-register-btn');
    if (googleRegisterBtn) {
        googleRegisterBtn.addEventListener('click', function() {
            // Apri la finestra di popup per l'autenticazione con Google
            openGoogleAuthWindow('register');
        });
    }
}

/**
 * Apre una finestra di popup per l'autenticazione con Google
 * @param {string} mode - Modalità di autenticazione ('login' o 'register')
 */
function openGoogleAuthWindow(mode) {
    // Ottieni eventuali parametri di query dalla URL corrente
    const urlParams = new URLSearchParams(window.location.search);
    const returnTo = urlParams.get('returnTo') || '';
    
    // Determina l'URL corretto in base alla modalità (login o register)
    const authUrl = `/api/auth/google?mode=${mode}&returnTo=${encodeURIComponent(returnTo)}`;
    
    // Apri la finestra di popup
    const popup = window.open(authUrl, 'googleAuth', 'width=500,height=600,resizable=yes');
    
    // Gestisci la comunicazione con la finestra di popup tramite messaggi
    const messageHandler = function(event) {
        // Verifica che il messaggio provenga dal dominio corretto
        if (event.origin !== window.location.origin) return;
        
        // Gestisci il risultato dell'autenticazione
        if (event.data.type === 'auth_success') {
            // L'autenticazione è avvenuta con successo
            console.log('Autenticazione con Google completata con successo!');
            
            // Salva i dati dell'utente
            if (event.data.user) {
                localStorage.setItem('user', JSON.stringify(event.data.user));
            }
            
            // Salva il token
            if (event.data.token) {
                localStorage.setItem('token', event.data.token);
            }
            
            // Chiudi il popup
            if (popup && !popup.closed) {
                popup.close();
            }
            
            // Elimina questo handler di messaggi
            window.removeEventListener('message', messageHandler);
            
            // Determina dove reindirizzare l'utente
            let redirectUrl = '/home';
            if (returnTo) {
                redirectUrl = `/${returnTo}`;
            }
            
            // Mostra un messaggio di successo
            if (window.showMessage) {
                const message = mode === 'login' 
                    ? 'Accesso con Google completato con successo! Reindirizzamento...'
                    : 'Registrazione con Google completata con successo! Reindirizzamento...';
                    
                window.showMessage(message, 'success');
                
                // Reindirizza dopo un breve delay
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
            } else {
                // Se non esiste la funzione showMessage, reindirizza immediatamente
                window.location.href = redirectUrl;
            }
        } else if (event.data.type === 'auth_error') {
            // L'autenticazione ha fallito
            console.error('Errore nell\'autenticazione con Google:', event.data.error);
            
            // Chiudi il popup
            if (popup && !popup.closed) {
                popup.close();
            }
            
            // Elimina questo handler di messaggi
            window.removeEventListener('message', messageHandler);
            
            // Mostra un messaggio di errore
            if (window.showMessage) {
                window.showMessage('Errore durante l\'autenticazione con Google: ' + (event.data.error || 'Si è verificato un errore imprevisto'), 'error');
            } else {
                alert('Errore durante l\'autenticazione con Google: ' + (event.data.error || 'Si è verificato un errore imprevisto'));
            }
        }
    };
    
    // Aggiungi l'handler di messaggi
    window.addEventListener('message', messageHandler);
    
    // Gestisci il caso in cui il popup venga chiuso manualmente
    const popupCheckInterval = setInterval(() => {
        if (popup.closed) {
            clearInterval(popupCheckInterval);
            window.removeEventListener('message', messageHandler);
        }
    }, 1000);
}