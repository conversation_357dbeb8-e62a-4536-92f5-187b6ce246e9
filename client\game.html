<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Skèmino - Partita Multiplayer Online">
    <meta name="theme-color" content="#172a45">
    <title>Skèmino - Partita Online</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="css/critical-layout.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multiplayer.css">
    <link rel="stylesheet" href="css/card-animations.css">
    <link rel="stylesheet" href="css/psn-unified.css">
    <link rel="stylesheet" href="css/board-snapshot.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/page-transitions.css">
    <link rel="stylesheet" href="css/dice-animation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Precaricamento immagini critiche -->
    <link rel="preload" href="img/carte/card-back.webp" as="image" type="image/webp" fetchpriority="high">
    <link rel="preload" href="img/carte/skemino.webp" as="image" type="image/webp" fetchpriority="high">
</head>
<body>
    <!-- Precarica le immagini di copertura delle carte -->
    <div class="preload-card-backs" aria-hidden="true">
        <img src="img/carte/card-back.webp" alt="Card Back" class="card-back-image"
             loading="eager" fetchpriority="high" decoding="sync">
        <img src="/img/Cover carte/cover.png" alt="Card Back" class="card-back-image"
             loading="eager" fetchpriority="high" decoding="sync">
    </div>

    <!-- Overlay scuro per l'animazione dei dadi -->
    <div id="dice-animation-overlay" class="dice-animation-overlay" data-hidden="true"></div>
    
    <!-- Area animazione dadi (mostrata durante la preparazione della partita) -->
    <div id="dice-animation-area" class="dice-animation-area" data-hidden="true">
        <div class="dice-animation-container">
            <h3>Preparazione Partita...</h3>
            <div id="dice-area" class="dice-area">
                <!-- I dadi appariranno qui -->
            </div>
            <p id="dice-result-text" class="dice-result-text">Lancio dei dadi per determinare chi inizia...</p>
        </div>
    </div>
    

    <!-- Container principale del gioco -->
    <div id="game-container" style="display: none;">
        <!-- Menu laterale compresso per l'interfaccia di gioco -->
        <div id="game-sidebar" class="game-sidebar">
            <div class="logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image">
            </div>
            <nav>
                <ul>
                    <li class="active"><i class="fas fa-play"></i><span>Gioca</span><div class="menu-tooltip">Gioca</div></li>
                    <li><i class="fas fa-book"></i><span>Regole</span><div class="menu-tooltip">Regole</div></li>
                    <li><i class="fas fa-trophy"></i><span>Tornei</span><div class="menu-tooltip">Tornei</div></li>
                    <li><i class="fas fa-graduation-cap"></i><span>Tutorial</span><div class="menu-tooltip">Tutorial</div></li>
                    <li><i class="fas fa-chart-line"></i><span>Statistiche</span><div class="menu-tooltip">Statistiche</div></li>
                    <li><i class="fas fa-users"></i><span>Community</span><div class="menu-tooltip">Community</div></li>
                    <li id="game-classifica-link"><i class="fas fa-medal"></i><span>Classifica</span><div class="menu-tooltip">Classifica</div></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <ul class="sidebar-icons">
                    <li id="game-user-profile">
                        <i class="fas fa-user"></i>
                        <div class="menu-tooltip" id="user-tooltip-text">Utente</div>
                    </li>
                    <li>
                        <i class="fas fa-globe"></i>
                        <div class="menu-tooltip">Italiano</div>
                    </li>
                    <li>
                        <i class="fas fa-question-circle"></i>
                        <div class="menu-tooltip">Supporto</div>
                    </li>
                </ul>
            </div>
        </div>

        <div id="players-column">
            <div id="player1-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot white"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player1-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player1-avatar-container"></div>
                                <div class="player-avatar-level" id="player1-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player1-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">30:00</span>
                        </div>
                    </div>
                </div>

                <div id="player1-hand" class="hand-area">
                    <!-- Carte del giocatore 1 (bianco) -->
                </div>
            </div>

            <div id="player2-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot black"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player2-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player2-avatar-container"></div>
                                <div class="player-avatar-level" id="player2-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player2-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">30:00</span>
                        </div>
                    </div>
                </div>

                <div id="player2-hand" class="hand-area">
                    <!-- Carte del giocatore 2 (nero) -->
                </div>
            </div>
        </div>

        <div id="board-area" style="position: relative;">
            <div id="advantage-indicator">
                <div class="advantage-white">
                    <span class="advantage-label">G1</span>
                </div>
                <div class="advantage-black">
                    <span class="advantage-label">G2</span>
                </div>
            </div>
            <div id="game-board" style="position: relative;">
                <!-- La griglia di gioco verrà generata qui -->
                
            </div>

            <div id="board-sidebar">
                <!-- Tab container -->
                <div id="sidebar-tabs" class="sidebar-tabs">
                    <div class="tab active" data-tab="gioca">
                        <i class="fas fa-gamepad"></i>
                        <span class="tab-text">Gioca</span>
                    </div>
                    <div class="tab" data-tab="nuova-partita">
                        <i class="fas fa-play"></i>
                        <span class="tab-text">Nuova Partita</span>
                    </div>
                    <div class="tab" data-tab="analisi">
                        <i class="fas fa-chart-bar"></i>
                        <span class="tab-text">Analisi</span>
                    </div>
                    <div class="tab" data-tab="giocatori">
                        <i class="fas fa-users"></i>
                        <span class="tab-text">Giocatori</span>
                    </div>
                </div>

                <!-- Tab content containers -->
                <div id="sidebar-content">
                    <!-- Gioca tab content -->
                    <div id="tab-gioca" class="tab-content active">
                        <div id="game-message-container">
                            <p id="game-message"></p>
                        </div>

                        <div class="game-actions">
                            <h4><i class="fas fa-chess"></i> Azioni di Gioco</h4>
                            <div class="action-buttons">
                                <button id="draw-card-button" class="action-button">
                                    <i class="fas fa-hand-paper"></i>
                                    <span>Pesca Carta</span>
                                    <span id="deck-counter" class="deck-counter">39</span>
                                </button>
                                <button id="hint-button" class="action-button">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Suggerimento</span>
                                </button>
                            </div>
                        </div>

                        <div class="game-status">
                            <h4><i class="fas fa-info-circle"></i> Stato Partita</h4>
                            <div class="status-info">
                                <div class="status-row">
                                    <span class="status-label">Dadi:</span>
                                    <span class="status-value" id="dice-result">-</span>
                                </div>

                                <div class="status-row move-navigation">
                                    <span class="status-label">Mosse:</span>
                                    <div class="move-navigation-controls">
                                        <button id="prev-move-btn" class="move-nav-btn" title="Mossa precedente">
                                            <i class="fas fa-arrow-left"></i>
                                        </button>
                                        <span class="move-counter" id="current-move-counter">0/0</span>
                                        <button id="next-move-btn" class="move-nav-btn" title="Mossa successiva">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                        <button id="current-move-btn" class="move-nav-btn" title="Torna alla mossa corrente">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Opzioni di fine partita integrate nello stato partita -->
                                <div class="status-row end-game-options-row">
                                    <span class="status-label">Opzioni:</span>
                                    <div class="end-game-buttons-integrated">
                                        <button id="offer-draw-button" class="end-game-button draw-button">
                                            <i class="fas fa-handshake"></i>
                                            <span>Chiedi Patta</span>
                                        </button>
                                        <button id="resign-button" class="end-game-button resign-button">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Abbandona</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Skèmino notation -->
                        <div id="skemino-notation-area">
                            <!-- PSN Visualizer sarà inizializzato qui -->
                        </div>

                        <!-- Chat per il multiplayer -->
                        <div id="chat-area" class="chat-area">
                            <div class="chat-header">
                                <i class="fas fa-comments"></i> Chat Multiplayer
                            </div>
                            <div id="chat-messages" class="chat-messages"></div>
                            <div class="chat-input-container">
                                <input type="text" id="chat-input" placeholder="Invia messaggio..." maxlength="100">
                                <button id="emoji-button" class="emoji-button">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                            <div id="emoji-picker" class="emoji-picker">
                                <div class="emoji-list">
                                    <span class="emoji-item">😀</span>
                                    <span class="emoji-item">😎</span>
                                    <span class="emoji-item">👍</span>
                                    <span class="emoji-item">👏</span>
                                    <span class="emoji-item">🎮</span>
                                    <span class="emoji-item">🤔</span>
                                    <span class="emoji-item">🙂</span>
                                    <span class="emoji-item">😊</span>
                                    <span class="emoji-item">👋</span>
                                    <span class="emoji-item">🏆</span>
                                    <span class="emoji-item">⭐</span>
                                    <span class="emoji-item">❤️</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Nuova Partita tab content -->
                    <div id="tab-nuova-partita" class="tab-content">
                        <div id="game-message-container-nuova-partita">
                            <p id="game-message-nuova-partita"></p>
                        </div>

                        <!-- Contenuto per utenti non autenticati -->
                        <div id="auth-required-content" class="auth-required-content" style="display: none;">
                            <h4><i class="fas fa-user-lock"></i> Accesso Richiesto</h4>
                            <p>Per giocare online è necessario effettuare l'accesso o registrarsi.</p>
                            <div class="auth-buttons-container">
                                <a href="login.html" class="auth-button login-button">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>Accedi</span>
                                </a>
                                <a href="register.html" class="auth-button register-button">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Registrati</span>
                                </a>
                            </div>
                        </div>

                        <!-- Contenuto per utenti autenticati -->
                        <div id="game-options-content" class="new-game-options">
                            <h4><i class="fas fa-play-circle"></i> Opzioni Nuova Partita</h4>
                            <div class="options-container">
                                <div class="option-row">
                                    <span class="option-label">Modalità:</span>
                                    <div class="option-value">
                                        <select id="game-mode-select" class="game-select">
                                            <option value="online">Online</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="option-row">
                                    <span class="option-label">Tempo per mossa:</span>
                                    <div class="option-value">
                                        <select id="time-select" class="game-select">
                                            <option value="none">Nessun limite</option>
                                            <option value="30">30 secondi</option>
                                            <option value="60">1 minuto</option>
                                            <option value="120">2 minuti</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="start-game-button-container">
                                <button id="start-new-game-button" class="start-game-button">
                                    <i class="fas fa-play"></i>
                                    <span>Nuova Partita</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Altri tab content omessi per brevità -->
                </div>
            </div>
        </div>
    </div>

    <!-- Mazzo di carte -->
    <div class="deck-area">
        <div class="deck-label" id="deck-label">Mazzo di gioco</div>
        <div id="deck">
            <div class="card-stack-visual static-deck">
                <div class="card-pile-1"></div>
                <div class="card-pile-2"></div>
                <div class="card-pile-3"></div>
                <div class="card-pile-4"></div>
                <div class="card-pile-5"></div>
            </div>
        </div>
    </div>

    <!-- Nuova posizione: Animazione nomi giocatori multiplayer -->
    <div id="setup-animation" style="display: none !important;">
        <!-- Animazione nomi giocatori -->
        <div class="player-names-animation">
            <div id="player1-name-flying" class="player-name-flying player1"></div>
            <div id="player2-name-flying" class="player-name-flying player2"></div>
        </div>
        
        <!-- Testo centrale -->
        <div class="setup-center-text">
            <h2>Partita Online</h2>
            <p>Preparazione in corso...</p>
        </div>
    </div>

    <!-- Victory Screen -->
    <div id="victory-screen" style="display: none;">
        <div class="victory-container">
            <div class="victory-header">
                <i class="fas fa-trophy victory-icon"></i>
                <h2>Vittoria!</h2>
            </div>
            <div class="victory-content">
                <p id="winner-name">Nome Vincitore</p>
                <div id="rating-changes">
                    <p id="winner-rating" class="rating-change winner-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                    <p id="loser-rating" class="rating-change loser-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                </div>
                <p id="victory-reason">Motivo della vittoria</p>
            </div>
            <div class="victory-buttons">
                <button id="examine-game-victory" class="primary-btn">
                    <i class="fas fa-search"></i> Esamina Partita
                </button>
                <button id="new-game-victory" class="secondary-btn">
                    <i class="fas fa-redo"></i> Nuova Partita
                </button>
            </div>
        </div>
    </div>

    <!-- Battle Start Overlay -->
    <div id="battle-start-overlay">
        <div class="battle-start-text"></div>
        <div class="battle-particles"></div>
    </div>

    <!-- Turn Timer -->
    <div id="turn-timer-container" class="turn-timer-container" style="display: none;">
        <div id="turn-timer" class="turn-timer">00:00</div>
    </div>

    <!-- Notifications -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Matchmaking Modal -->
    <div id="matchmaking-modal" class="matchmaking-modal">
        <div class="matchmaking-content">
            <h2>Ricerca Avversario</h2>
            <div class="matchmaking-spinner"></div>
            <p id="matchmaking-status" class="matchmaking-status">Ricerca di un avversario in corso...</p>
            <div class="matchmaking-buttons">
                <button id="cancel-matchmaking" class="cancel-matchmaking"><i class="fas fa-times-circle"></i> Annulla Ricerca</button>
            </div>
        </div>
    </div>

    <!-- Modali per login/registrazione -->
    <div id="login-popup" class="game-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4><i class="fas fa-sign-in-alt"></i> Accedi per giocare online</h4>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <p>Per giocare online è necessario effettuare l'accesso.</p>
                <div class="form-group">
                    <label for="popup-login-username"><i class="fas fa-user"></i> Username</label>
                    <input type="text" id="popup-login-username" placeholder="Inserisci il tuo username" required>
                </div>
                <div class="form-group">
                    <label for="popup-login-password"><i class="fas fa-lock"></i> Password</label>
                    <input type="password" id="popup-login-password" placeholder="Inserisci la tua password" required>
                </div>
                <p id="login-error-message" class="error-message"></p>
                <div class="remember-forgot">
                    <div class="remember-me">
                        <input type="checkbox" id="popup-remember-me">
                        <label for="popup-remember-me">Ricordami</label>
                    </div>
                    <a href="#" id="popup-forgot-password">Password dimenticata?</a>
                </div>
            </div>
            <div class="modal-footer">
                <button id="popup-login-button" class="confirm-button">
                    <i class="fas fa-sign-in-alt"></i> Accedi
                </button>
                <div class="register-link">
                    Non hai un account? <a href="#" id="popup-switch-to-register">Registrati</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Popup -->
    <div id="register-popup" class="game-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h4><i class="fas fa-user-plus"></i> Registrati per giocare online</h4>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <p>Crea un account gratuito per giocare online contro altri giocatori.</p>
                <div class="form-group">
                    <label for="popup-register-username"><i class="fas fa-user"></i> Username</label>
                    <input type="text" id="popup-register-username" placeholder="Scegli un username" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-email"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="popup-register-email" placeholder="Inserisci la tua email" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-password"><i class="fas fa-lock"></i> Password</label>
                    <input type="password" id="popup-register-password" placeholder="Crea una password" required>
                </div>
                <div class="form-group">
                    <label for="popup-register-confirm-password"><i class="fas fa-lock"></i> Conferma Password</label>
                    <input type="password" id="popup-register-confirm-password" placeholder="Conferma la tua password" required>
                </div>
                <p id="register-error-message" class="error-message"></p>
                <div class="terms-container">
                    <input type="checkbox" id="popup-terms-agreement" required>
                    <label for="popup-terms-agreement">Accetto i termini e le condizioni</label>
                </div>
            </div>
            <div class="modal-footer">
                <button id="popup-register-button" class="confirm-button">
                    <i class="fas fa-user-plus"></i> Registrati
                </button>
                <div class="login-link">
                    Hai già un account? <a href="#" id="popup-switch-to-login">Accedi</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="auth.js"></script>
    <script src="js/token-manager.js"></script>
    <script src="js/game-state-manager.js"></script>

    <script src="script.js"></script>
    <script src="js/multiplayer.js"></script>
    <script src="js/psn-unified.js"></script>
    <script src="js/page-transitions.js"></script>
    <!-- Game mode managers -->
    <script src="js/local-game.js"></script>
    <script src="js/online-game.js"></script>
    <script src="js/game-mode-manager.js"></script>
    <script src="js/online-play-enhancer.js"></script>
    <script src="js/player-names-protection.js"></script>
    <script src="js/smooth-loading.js"></script>
    <script src="victory-fix.js"></script>
    <script src="js/game-auth-check.js"></script>
</body>
</html>